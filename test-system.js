#!/usr/bin/env node

/**
 * Simple system test for OAuth2 authentication flow
 */

async function testSystem() {
  console.log('🧪 Testing OAuth2 System\n');

  // Test 1: OAuth2 Provider health
  console.log('1. Testing OAuth2 Provider...');
  try {
    // Test authorization endpoint instead of discovery (which may not be enabled)
    const authUrl = 'http://localhost:3002/auth?response_type=code&client_id=oauth2-client-123&redirect_uri=http://localhost:3001/callback&scope=openid&state=test123';
    const response = await fetch(authUrl, { redirect: 'manual' });

    if (response.status === 302 || response.status === 303) {
      console.log('✅ OAuth2 Provider is running');
      console.log('   Authorization endpoint: Working');
      console.log('   Token endpoint: http://localhost:3002/token');
    } else {
      console.log('❌ OAuth2 Provider not responding properly');
      return;
    }
  } catch (error) {
    console.log('❌ OAuth2 Provider error:', error.message);
    return;
  }

  // Test 2: OAuth2 Client health
  console.log('\n2. Testing OAuth2 Client...');
  try {
    const response = await fetch('http://localhost:3001');
    if (response.ok) {
      const html = await response.text();
      const hasLoginButton = html.includes('Login with OAuth2');
      console.log('✅ OAuth2 Client is running');
      console.log('   Login button present:', hasLoginButton ? 'Yes' : 'No');
    } else {
      console.log('❌ OAuth2 Client not responding');
      return;
    }
  } catch (error) {
    console.log('❌ OAuth2 Client error:', error.message);
    return;
  }

  // Test 3: Client login flow
  console.log('\n3. Testing Client Login Flow...');
  try {
    const response = await fetch('http://localhost:3001/login', { redirect: 'manual' });

    if (response.status === 302 || response.status === 303) {
      const location = response.headers.get('location');
      const hasPKCE = location && location.includes('code_challenge');
      console.log('✅ Client login flow working');
      console.log('   Redirects to OAuth2 provider:', location ? 'Yes' : 'No');
      console.log('   PKCE parameters included:', hasPKCE ? 'Yes' : 'No');
    } else {
      console.log('❌ Client login flow failed:', response.status);
    }
  } catch (error) {
    console.log('❌ Client login flow error:', error.message);
  }

  console.log('\n🎉 System Test Complete!');
  console.log('\n📋 Manual Testing Instructions:');
  console.log('1. Open http://localhost:3001 in your browser');
  console.log('2. Click "Login with OAuth2"');
  console.log('3. Use any username/password in the dev login form');
  console.log('4. You should be redirected back with user information');
  console.log('\n🔧 Services:');
  console.log('   OAuth2 Provider: http://localhost:3002');
  console.log('   OAuth2 Client: http://localhost:3001');
}

testSystem().catch(console.error);
