#!/usr/bin/env node

// Simple test script to demonstrate MCP functionality
const { spawn } = require('child_process');

console.log('🚀 Testing MCP Server...\n');

// Spawn the MCP server
const mcpServer = spawn('pnpm', ['exec', 'tsx', 'src/mcp-server.ts'], {
  stdio: ['pipe', 'pipe', 'inherit']
});

// Test messages
const messages = [
  // Initialize
  {
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    }
  },
  // List tools
  {
    jsonrpc: "2.0",
    id: 2,
    method: "tools/list"
  },
  // Call addition tool
  {
    jsonrpc: "2.0",
    id: 3,
    method: "tools/call",
    params: {
      name: "add",
      arguments: { a: 15, b: 27 }
    }
  },
  // List resources
  {
    jsonrpc: "2.0",
    id: 4,
    method: "resources/list"
  },
  // Read greeting resource
  {
    jsonrpc: "2.0",
    id: 5,
    method: "resources/read",
    params: {
      uri: "greeting://Alice"
    }
  }
];

let messageIndex = 0;
let responses = [];

mcpServer.stdout.on('data', (data) => {
  const lines = data.toString().split('\n').filter(line => line.trim());
  
  lines.forEach(line => {
    if (line.startsWith('{')) {
      try {
        const response = JSON.parse(line);
        responses.push(response);
        
        console.log(`📨 Response ${response.id}:`);
        if (response.result) {
          if (response.result.tools) {
            console.log(`   Found ${response.result.tools.length} tools:`);
            response.result.tools.forEach(tool => {
              console.log(`     - ${tool.name}: ${tool.description}`);
            });
          } else if (response.result.content) {
            console.log(`   Tool result: ${response.result.content[0].text}`);
          } else if (response.result.resources) {
            console.log(`   Found ${response.result.resources.length} resources:`);
            response.result.resources.forEach(resource => {
              console.log(`     - ${resource.name}: ${resource.description}`);
            });
          } else if (response.result.contents) {
            console.log(`   Resource content: ${response.result.contents[0].text}`);
          } else if (response.result.capabilities) {
            console.log(`   Server capabilities: ${Object.keys(response.result.capabilities).join(', ')}`);
          }
        }
        console.log();
        
        // Send next message
        if (messageIndex < messages.length) {
          const nextMessage = messages[messageIndex++];
          console.log(`📤 Sending: ${nextMessage.method}`);
          mcpServer.stdin.write(JSON.stringify(nextMessage) + '\n');
        } else {
          // All done
          console.log('✅ All tests completed successfully!');
          console.log('\n🎉 OAuth2 + MCP integration is working!');
          console.log('\nNext steps:');
          console.log('1. Start the services: pnpm run dev:all');
          console.log('2. Open http://localhost:3001 in your browser');
          console.log('3. Login with OAuth2 (any username/password works)');
          console.log('4. Test the MCP tools in the web interface');
          
          mcpServer.kill();
          process.exit(0);
        }
      } catch (e) {
        // Ignore non-JSON lines
      }
    } else {
      console.log(`📝 Server: ${line}`);
    }
  });
});

mcpServer.on('error', (error) => {
  console.error('❌ Error starting MCP server:', error);
  process.exit(1);
});

// Start the test
console.log('📤 Sending: initialize');
mcpServer.stdin.write(JSON.stringify(messages[messageIndex++]) + '\n');

// Timeout after 30 seconds
setTimeout(() => {
  console.log('⏰ Test timeout');
  mcpServer.kill();
  process.exit(1);
}, 30000);
