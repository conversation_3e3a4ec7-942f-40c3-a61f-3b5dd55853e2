#!/usr/bin/env node

/**
 * Test script to demonstrate OAuth2 session behavior
 */

async function testSessionBehavior() {
  console.log('🔐 Testing OAuth2 Session Behavior\n');

  console.log('This test demonstrates OAuth2 session behavior:');
  console.log('1. First login requires authentication');
  console.log('2. Subsequent logins may skip authentication (SSO)');
  console.log('3. With prompt=login, authentication is always required\n');

  // Test 1: Normal login flow
  console.log('1. Testing normal login flow...');
  try {
    const response = await fetch('http://localhost:3001/login', { redirect: 'manual' });
    
    if (response.status === 302 || response.status === 303) {
      const location = response.headers.get('location');
      const hasPromptLogin = location && location.includes('prompt=login');
      
      console.log('✅ Login flow initiated');
      console.log('   Redirect URL includes prompt=login:', hasPromptLogin ? 'Yes' : 'No');
      
      if (hasPromptLogin) {
        console.log('   → This will force re-authentication every time');
      } else {
        console.log('   → This will use SSO if user is already authenticated');
      }
    } else {
      console.log('❌ Login flow failed');
    }
  } catch (error) {
    console.log('❌ Login flow error:', error.message);
  }

  console.log('\n📋 Session Behavior Explanation:');
  console.log('');
  console.log('🔹 Standard OAuth2 Behavior (without prompt=login):');
  console.log('   • User logs in once at the OAuth2 provider');
  console.log('   • Provider sets a session cookie');
  console.log('   • Subsequent authorization requests check existing session');
  console.log('   • If session is valid, user is not prompted to login again');
  console.log('   • This enables Single Sign-On (SSO) across applications');
  console.log('');
  console.log('🔹 Forced Re-authentication (with prompt=login):');
  console.log('   • Every authorization request forces user to login');
  console.log('   • Existing session is ignored');
  console.log('   • User must enter credentials every time');
  console.log('   • More secure but less user-friendly');
  console.log('');
  console.log('🔹 Current Configuration:');
  console.log('   • prompt=login is now ENABLED');
  console.log('   • Users will need to authenticate every time');
  console.log('   • To disable: remove prompt=login from authorization URL');
  console.log('');
  console.log('🔹 Session Management:');
  console.log('   • Provider session TTL: Based on oidc-provider defaults');
  console.log('   • Client session TTL: 1 hour (Max-Age=3600)');
  console.log('   • Access token TTL: 1 hour');
  console.log('   • Refresh token TTL: 1 day');
  console.log('');
  console.log('🧪 Manual Testing:');
  console.log('1. Open http://localhost:3001 in browser');
  console.log('2. Click "Login with OAuth2" and complete authentication');
  console.log('3. Click "Logout" then "Login with OAuth2" again');
  console.log('4. With prompt=login: You will be asked to authenticate again');
  console.log('5. Without prompt=login: You might skip authentication (SSO)');
}

testSessionBehavior().catch(console.error);
