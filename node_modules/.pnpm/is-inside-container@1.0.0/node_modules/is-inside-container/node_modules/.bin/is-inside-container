#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/is-inside-container@1.0.0/node_modules/is-inside-container/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/is-inside-container@1.0.0/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/is-inside-container@1.0.0/node_modules/is-inside-container/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/is-inside-container@1.0.0/node_modules:/Users/<USER>/Documents/augment-projects/aug-oldc-test/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../cli.js" "$@"
else
  exec node  "$basedir/../../cli.js" "$@"
fi
