{"name": "inflation", "description": "Easily unzip an HTTP stream", "version": "2.1.0", "author": "<PERSON> <<EMAIL>> (http://jongleberry.com)", "license": "MIT", "repository": "stream-utils/inflation", "keywords": ["decompress", "unzip", "inflate", "zlib", "gunzip", "brotli"], "devDependencies": {"git-contributor": "^2.1.5", "istanbul": "0.2.10", "mocha": "^10.2.0", "readable-stream": "~1.0.27", "should": "4.0.4"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail test/*.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "contributor": "git-contributor"}, "files": ["index.js"]}