"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var jsx_exports = {};
__export(jsx_exports, {
  Children: () => import_children.Children,
  ErrorBoundary: () => import_components.ErrorBoundary,
  Fragment: () => import_base.Fragment,
  StrictMode: () => import_base.Fragment,
  Suspense: () => import_streaming.Suspense,
  cloneElement: () => import_base.cloneElement,
  createContext: () => import_context.createContext,
  createElement: () => import_base.jsx,
  createRef: () => import_hooks2.createRef,
  default: () => jsx_default,
  forwardRef: () => import_hooks2.forwardRef,
  isValidElement: () => import_base.isValidElement,
  jsx: () => import_base.jsx,
  memo: () => import_base.memo,
  startTransition: () => import_hooks2.startTransition,
  startViewTransition: () => import_hooks2.startViewTransition,
  use: () => import_hooks2.use,
  useActionState: () => import_hooks.useActionState,
  useCallback: () => import_hooks2.useCallback,
  useContext: () => import_context.useContext,
  useDebugValue: () => import_hooks2.useDebugValue,
  useDeferredValue: () => import_hooks2.useDeferredValue,
  useEffect: () => import_hooks2.useEffect,
  useId: () => import_hooks2.useId,
  useImperativeHandle: () => import_hooks2.useImperativeHandle,
  useInsertionEffect: () => import_hooks2.useInsertionEffect,
  useLayoutEffect: () => import_hooks2.useLayoutEffect,
  useMemo: () => import_hooks2.useMemo,
  useOptimistic: () => import_hooks.useOptimistic,
  useReducer: () => import_hooks2.useReducer,
  useRef: () => import_hooks2.useRef,
  useState: () => import_hooks2.useState,
  useSyncExternalStore: () => import_hooks2.useSyncExternalStore,
  useTransition: () => import_hooks2.useTransition,
  useViewTransition: () => import_hooks2.useViewTransition,
  version: () => import_base.reactAPICompatVersion
});
module.exports = __toCommonJS(jsx_exports);
var import_base = require("./base");
var import_children = require("./children");
var import_components = require("./components");
var import_context = require("./context");
var import_hooks = require("./dom/hooks");
var import_hooks2 = require("./hooks");
var import_streaming = require("./streaming");
var jsx_default = {
  version: import_base.reactAPICompatVersion,
  memo: import_base.memo,
  Fragment: import_base.Fragment,
  StrictMode: import_base.Fragment,
  isValidElement: import_base.isValidElement,
  createElement: import_base.jsx,
  cloneElement: import_base.cloneElement,
  ErrorBoundary: import_components.ErrorBoundary,
  createContext: import_context.createContext,
  useContext: import_context.useContext,
  useState: import_hooks2.useState,
  useEffect: import_hooks2.useEffect,
  useRef: import_hooks2.useRef,
  useCallback: import_hooks2.useCallback,
  useReducer: import_hooks2.useReducer,
  useId: import_hooks2.useId,
  useDebugValue: import_hooks2.useDebugValue,
  use: import_hooks2.use,
  startTransition: import_hooks2.startTransition,
  useTransition: import_hooks2.useTransition,
  useDeferredValue: import_hooks2.useDeferredValue,
  startViewTransition: import_hooks2.startViewTransition,
  useViewTransition: import_hooks2.useViewTransition,
  useMemo: import_hooks2.useMemo,
  useLayoutEffect: import_hooks2.useLayoutEffect,
  useInsertionEffect: import_hooks2.useInsertionEffect,
  createRef: import_hooks2.createRef,
  forwardRef: import_hooks2.forwardRef,
  useImperativeHandle: import_hooks2.useImperativeHandle,
  useSyncExternalStore: import_hooks2.useSyncExternalStore,
  useActionState: import_hooks.useActionState,
  useOptimistic: import_hooks.useOptimistic,
  Suspense: import_streaming.Suspense,
  Children: import_children.Children
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Children,
  ErrorBoundary,
  Fragment,
  StrictMode,
  Suspense,
  cloneElement,
  createContext,
  createElement,
  createRef,
  forwardRef,
  isValidElement,
  jsx,
  memo,
  startTransition,
  startViewTransition,
  use,
  useActionState,
  useCallback,
  useContext,
  useDebugValue,
  useDeferredValue,
  useEffect,
  useId,
  useImperativeHandle,
  useInsertionEffect,
  useLayoutEffect,
  useMemo,
  useOptimistic,
  useReducer,
  useRef,
  useState,
  useSyncExternalStore,
  useTransition,
  useViewTransition,
  version
});
