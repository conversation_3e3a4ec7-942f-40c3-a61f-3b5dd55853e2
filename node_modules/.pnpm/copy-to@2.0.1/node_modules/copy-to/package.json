{"name": "copy-to", "version": "2.0.1", "description": "copy an object's properties to another object", "main": "index.js", "files": ["index.js"], "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/node-modules/copy-to.git"}, "keywords": ["copy", "object", "properties", "setter", "getter"], "author": "dead_horse <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/node-modules/copy-to/issues"}, "homepage": "https://github.com/node-modules/copy-to", "devDependencies": {"mocha": "*", "should": "*"}}