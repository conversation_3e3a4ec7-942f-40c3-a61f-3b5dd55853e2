hoistPattern:
  - '*'
hoistedDependencies:
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@esbuild/aix-ppc64@0.25.6':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.6':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.6':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.6':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.6':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.6':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.6':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.6':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.6':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.6':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.6':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.6':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.6':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.6':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.6':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.6':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.6':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.6':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.6':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.6':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.6':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.6':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.6':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.6':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.6':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.6':
    '@esbuild/win32-x64': private
  '@floating-ui/core@1.7.2':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.2':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@koa/cors@5.0.0':
    '@koa/cors': private
  '@koa/router@13.1.1':
    '@koa/router': private
  '@modelcontextprotocol/inspector-cli@0.16.1':
    '@modelcontextprotocol/inspector-cli': private
  '@modelcontextprotocol/inspector-client@0.16.1':
    '@modelcontextprotocol/inspector-client': private
  '@modelcontextprotocol/inspector-server@0.16.1':
    '@modelcontextprotocol/inspector-server': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-checkbox@1.3.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-checkbox': private
  '@radix-ui/react-collection@1.1.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(react@18.3.1)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(react@18.3.1)':
    '@radix-ui/react-context': private
  '@radix-ui/react-dialog@1.1.14(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dialog': private
  '@radix-ui/react-direction@1.1.1(react@18.3.1)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.10(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.2(react@18.3.1)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-icons@1.3.2(react@18.3.1)':
    '@radix-ui/react-icons': private
  '@radix-ui/react-id@1.1.1(react@18.3.1)':
    '@radix-ui/react-id': private
  '@radix-ui/react-label@2.1.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-label': private
  '@radix-ui/react-popover@1.1.14(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-popover': private
  '@radix-ui/react-popper@1.2.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.9(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.10(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-select@2.2.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-select': private
  '@radix-ui/react-slot@1.2.3(react@18.3.1)':
    '@radix-ui/react-slot': private
  '@radix-ui/react-tabs@1.1.12(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-tabs': private
  '@radix-ui/react-toast@1.2.14(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-toast': private
  '@radix-ui/react-tooltip@1.2.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-tooltip': private
  '@radix-ui/react-use-callback-ref@1.1.1(react@18.3.1)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(react@18.3.1)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(react@18.3.1)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(react@18.3.1)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.1(react@18.3.1)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(react@18.3.1)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(react@18.3.1)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(react@18.3.1)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@sindresorhus/is@5.6.0':
    '@sindresorhus/is': private
  '@szmarczak/http-timer@5.0.1':
    '@szmarczak/http-timer': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': private
  accepts@2.0.0:
    accepts: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  arg@4.1.3:
    arg: private
  aria-hidden@1.2.6:
    aria-hidden: private
  balanced-match@1.0.2:
    balanced-match: private
  body-parser@2.2.0:
    body-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  bundle-name@4.1.0:
    bundle-name: private
  bytes@3.1.2:
    bytes: private
  cache-content-type@1.0.1:
    cache-content-type: private
  cacheable-lookup@7.0.0:
    cacheable-lookup: private
  cacheable-request@10.2.14:
    cacheable-request: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  chalk@4.1.2:
    chalk: private
  class-variance-authority@0.7.1:
    class-variance-authority: private
  cliui@8.0.1:
    cliui: private
  clsx@2.1.1:
    clsx: private
  cmdk@1.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    cmdk: private
  co@4.6.0:
    co: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  commander@13.1.0:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  cookies@0.9.1:
    cookies: private
  cors@2.8.5:
    cors: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  debug@4.4.1:
    debug: private
  decompress-response@6.0.0:
    decompress-response: private
  deep-equal@1.0.1:
    deep-equal: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  defer-to-connect@2.0.1:
    defer-to-connect: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  delegates@1.0.0:
    delegates: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-node-es@1.1.0:
    detect-node-es: private
  diff@4.0.2:
    diff: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  esbuild@0.25.6:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  eta@3.5.0:
    eta: private
  etag@1.8.1:
    etag: private
  eventsource-parser@3.0.3:
    eventsource-parser: private
  eventsource@3.0.7:
    eventsource: private
  express-rate-limit@7.5.1(express@5.1.0):
    express-rate-limit: private
  express@5.1.0:
    express: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  finalhandler@2.1.0:
    finalhandler: private
  form-data-encoder@2.1.4:
    form-data-encoder: private
  forwarded@0.2.0:
    forwarded: private
  fresh@2.0.0:
    fresh: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  gopd@1.2.0:
    gopd: private
  got@13.0.0:
    got: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  http-assert@1.5.0:
    http-assert: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  http-errors@2.0.0:
    http-errors: private
  http2-wrapper@2.2.1:
    http2-wrapper: private
  iconv-lite@0.6.3:
    iconv-lite: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-docker@3.0.0:
    is-docker: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-promise@4.0.0:
    is-promise: private
  is-regex@1.2.1:
    is-regex: private
  is-wsl@3.1.0:
    is-wsl: private
  isexe@2.0.0:
    isexe: private
  jose@5.10.0:
    jose: private
  js-tokens@4.0.0:
    js-tokens: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  keygrip@1.1.0:
    keygrip: private
  keyv@4.5.4:
    keyv: private
  koa-compose@4.1.0:
    koa-compose: private
  koa-convert@2.0.0:
    koa-convert: private
  koa@2.16.1:
    koa: private
  lodash@4.17.21:
    lodash: private
  loose-envify@1.4.0:
    loose-envify: private
  lowercase-keys@3.0.0:
    lowercase-keys: private
  lucide-react@0.523.0(react@18.3.1):
    lucide-react: private
  make-error@1.3.6:
    make-error: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  mimic-response@4.0.0:
    mimic-response: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.1.3:
    ms: private
  nanoid@5.1.5:
    nanoid: private
  negotiator@0.6.3:
    negotiator: private
  normalize-url@8.0.2:
    normalize-url: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  oidc-token-hash@5.1.0:
    oidc-token-hash: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  only@0.0.2:
    only: private
  p-cancelable@3.0.0:
    p-cancelable: private
  parseurl@1.3.3:
    parseurl: private
  path-is-inside@1.0.2:
    path-is-inside: private
  path-key@3.1.1:
    path-key: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  pkce-challenge@5.0.0:
    pkce-challenge: private
  prismjs@1.30.0:
    prismjs: private
  proxy-addr@2.0.7:
    proxy-addr: private
  punycode@2.3.1:
    punycode: private
  qs@6.14.0:
    qs: private
  quick-lru@7.0.1:
    quick-lru: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  react-dom@18.3.1(react@18.3.1):
    react-dom: private
  react-remove-scroll-bar@2.3.8(react@18.3.1):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.1(react@18.3.1):
    react-remove-scroll: private
  react-simple-code-editor@0.14.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-simple-code-editor: private
  react-style-singleton@2.2.3(react@18.3.1):
    react-style-singleton: private
  react@18.3.1:
    react: private
  require-directory@2.1.1:
    require-directory: private
  resolve-alpn@1.2.1:
    resolve-alpn: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  responselike@3.0.0:
    responselike: private
  router@2.2.0:
    router: private
  run-applescript@7.0.0:
    run-applescript: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.23.2:
    scheduler: private
  send@1.2.0:
    send: private
  serve-handler@6.1.6:
    serve-handler: private
  serve-static@2.2.0:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  spawn-rx@5.1.2:
    spawn-rx: private
  statuses@2.0.1:
    statuses: private
  string-width@4.2.3:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
  supports-color@8.1.1:
    supports-color: private
  tailwind-merge@2.6.0:
    tailwind-merge: private
  tailwindcss-animate@1.0.7:
    tailwindcss-animate: private
  toidentifier@1.0.1:
    toidentifier: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-node@10.9.2(@types/node@20.19.8)(typescript@5.8.3):
    ts-node: private
  tslib@2.8.1:
    tslib: private
  tsscmp@1.0.6:
    tsscmp: private
  type-is@2.0.1:
    type-is: private
  undici-types@6.21.0:
    undici-types: private
  unpipe@1.0.0:
    unpipe: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.3(react@18.3.1):
    use-callback-ref: private
  use-sidecar@1.1.3(react@18.3.1):
    use-sidecar: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  vary@1.1.2:
    vary: private
  which@2.0.2:
    which: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.3:
    ws: private
  wsl-utils@0.1.0:
    wsl-utils: private
  y18n@5.0.8:
    y18n: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  ylru@1.4.0:
    ylru: private
  yn@3.1.1:
    yn: private
  zod-to-json-schema@3.24.6(zod@3.25.76):
    zod-to-json-schema: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.6.0
pendingBuilds: []
prunedAt: Wed, 16 Jul 2025 03:37:46 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.6'
  - '@esbuild/android-arm64@0.25.6'
  - '@esbuild/android-arm@0.25.6'
  - '@esbuild/android-x64@0.25.6'
  - '@esbuild/darwin-x64@0.25.6'
  - '@esbuild/freebsd-arm64@0.25.6'
  - '@esbuild/freebsd-x64@0.25.6'
  - '@esbuild/linux-arm64@0.25.6'
  - '@esbuild/linux-arm@0.25.6'
  - '@esbuild/linux-ia32@0.25.6'
  - '@esbuild/linux-loong64@0.25.6'
  - '@esbuild/linux-mips64el@0.25.6'
  - '@esbuild/linux-ppc64@0.25.6'
  - '@esbuild/linux-riscv64@0.25.6'
  - '@esbuild/linux-s390x@0.25.6'
  - '@esbuild/linux-x64@0.25.6'
  - '@esbuild/netbsd-arm64@0.25.6'
  - '@esbuild/netbsd-x64@0.25.6'
  - '@esbuild/openbsd-arm64@0.25.6'
  - '@esbuild/openbsd-x64@0.25.6'
  - '@esbuild/openharmony-arm64@0.25.6'
  - '@esbuild/sunos-x64@0.25.6'
  - '@esbuild/win32-arm64@0.25.6'
  - '@esbuild/win32-ia32@0.25.6'
  - '@esbuild/win32-x64@0.25.6'
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
