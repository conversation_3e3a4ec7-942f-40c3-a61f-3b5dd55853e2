hoistPattern:
  - '*'
hoistedDependencies:
  '@esbuild/aix-ppc64@0.25.6':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.6':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.6':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.6':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.6':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.6':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.6':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.6':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.6':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.6':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.6':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.6':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.6':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.6':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.6':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.6':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.6':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.6':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.6':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.6':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.6':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.6':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.6':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.6':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.6':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.6':
    '@esbuild/win32-x64': private
  '@koa/cors@5.0.0':
    '@koa/cors': private
  '@koa/router@13.1.1':
    '@koa/router': private
  '@sindresorhus/is@5.6.0':
    '@sindresorhus/is': private
  '@szmarczak/http-timer@5.0.1':
    '@szmarczak/http-timer': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/express-serve-static-core@5.0.7':
    '@types/express-serve-static-core': private
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  accepts@2.0.0:
    accepts: private
  ajv@6.12.6:
    ajv: private
  body-parser@2.2.0:
    body-parser: private
  bytes@3.1.2:
    bytes: private
  cache-content-type@1.0.1:
    cache-content-type: private
  cacheable-lookup@7.0.0:
    cacheable-lookup: private
  cacheable-request@10.2.14:
    cacheable-request: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  co@4.6.0:
    co: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  cookies@0.9.1:
    cookies: private
  cross-spawn@7.0.6:
    cross-spawn: private
  debug@4.4.1:
    debug: private
  decompress-response@6.0.0:
    decompress-response: private
  deep-equal@1.0.1:
    deep-equal: private
  defer-to-connect@2.0.1:
    defer-to-connect: private
  delegates@1.0.0:
    delegates: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  encodeurl@2.0.0:
    encodeurl: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  esbuild@0.25.6:
    esbuild: private
  escape-html@1.0.3:
    escape-html: private
  eta@3.5.0:
    eta: private
  etag@1.8.1:
    etag: private
  eventsource-parser@3.0.3:
    eventsource-parser: private
  eventsource@3.0.7:
    eventsource: private
  express-rate-limit@7.5.1(express@5.1.0):
    express-rate-limit: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  finalhandler@2.1.0:
    finalhandler: private
  form-data-encoder@2.1.4:
    form-data-encoder: private
  forwarded@0.2.0:
    forwarded: private
  fresh@2.0.0:
    fresh: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  gopd@1.2.0:
    gopd: private
  got@13.0.0:
    got: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  http-assert@1.5.0:
    http-assert: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  http-errors@2.0.0:
    http-errors: private
  http2-wrapper@2.2.1:
    http2-wrapper: private
  iconv-lite@0.6.3:
    iconv-lite: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-promise@4.0.0:
    is-promise: private
  is-regex@1.2.1:
    is-regex: private
  isexe@2.0.0:
    isexe: private
  jose@5.10.0:
    jose: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  keygrip@1.1.0:
    keygrip: private
  keyv@4.5.4:
    keyv: private
  koa-compose@4.1.0:
    koa-compose: private
  koa-convert@2.0.0:
    koa-convert: private
  koa@2.16.1:
    koa: private
  lowercase-keys@3.0.0:
    lowercase-keys: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@1.1.0:
    media-typer: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  mimic-response@4.0.0:
    mimic-response: private
  ms@2.1.3:
    ms: private
  nanoid@5.1.5:
    nanoid: private
  negotiator@1.0.0:
    negotiator: private
  normalize-url@8.0.2:
    normalize-url: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  oidc-token-hash@5.1.0:
    oidc-token-hash: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  only@0.0.2:
    only: private
  p-cancelable@3.0.0:
    p-cancelable: private
  parseurl@1.3.3:
    parseurl: private
  path-key@3.1.1:
    path-key: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  pkce-challenge@5.0.0:
    pkce-challenge: private
  proxy-addr@2.0.7:
    proxy-addr: private
  punycode@2.3.1:
    punycode: private
  qs@6.14.0:
    qs: private
  quick-lru@7.0.1:
    quick-lru: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  resolve-alpn@1.2.1:
    resolve-alpn: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  responselike@3.0.0:
    responselike: private
  router@2.2.0:
    router: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  send@1.2.0:
    send: private
  serve-static@2.2.0:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  statuses@2.0.1:
    statuses: private
  toidentifier@1.0.1:
    toidentifier: private
  tsscmp@1.0.6:
    tsscmp: private
  type-is@2.0.1:
    type-is: private
  undici-types@6.21.0:
    undici-types: private
  unpipe@1.0.0:
    unpipe: private
  uri-js@4.4.1:
    uri-js: private
  vary@1.1.2:
    vary: private
  which@2.0.2:
    which: private
  wrappy@1.0.2:
    wrappy: private
  ylru@1.4.0:
    ylru: private
  zod-to-json-schema@3.24.6(zod@3.25.76):
    zod-to-json-schema: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.6.0
pendingBuilds: []
prunedAt: Wed, 16 Jul 2025 03:37:46 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.6'
  - '@esbuild/android-arm64@0.25.6'
  - '@esbuild/android-arm@0.25.6'
  - '@esbuild/android-x64@0.25.6'
  - '@esbuild/darwin-x64@0.25.6'
  - '@esbuild/freebsd-arm64@0.25.6'
  - '@esbuild/freebsd-x64@0.25.6'
  - '@esbuild/linux-arm64@0.25.6'
  - '@esbuild/linux-arm@0.25.6'
  - '@esbuild/linux-ia32@0.25.6'
  - '@esbuild/linux-loong64@0.25.6'
  - '@esbuild/linux-mips64el@0.25.6'
  - '@esbuild/linux-ppc64@0.25.6'
  - '@esbuild/linux-riscv64@0.25.6'
  - '@esbuild/linux-s390x@0.25.6'
  - '@esbuild/linux-x64@0.25.6'
  - '@esbuild/netbsd-arm64@0.25.6'
  - '@esbuild/netbsd-x64@0.25.6'
  - '@esbuild/openbsd-arm64@0.25.6'
  - '@esbuild/openbsd-x64@0.25.6'
  - '@esbuild/openharmony-arm64@0.25.6'
  - '@esbuild/sunos-x64@0.25.6'
  - '@esbuild/win32-arm64@0.25.6'
  - '@esbuild/win32-ia32@0.25.6'
  - '@esbuild/win32-x64@0.25.6'
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
