# OAuth2 Authentication Flow - Project Summary

## 项目概述

本项目使用 node-oidc-provider 实现了一套完整的 OAuth2 认证流程，包含 OAuth2 提供者和客户端应用。项目使用 TypeScript、Hono 框架和 pnpm 包管理器构建。

## 技术栈

- **后端框架**: Hono (现代 TypeScript Web 框架)
- **OAuth2 实现**: node-oidc-provider (OpenID Connect 提供者)
- **包管理器**: pnpm
- **语言**: TypeScript
- **运行时**: Node.js
- **安全特性**: HMAC 客户端密钥、PKCE 支持、CSRF 保护

## 项目结构

```
├── src/
│   ├── index.ts          # OAuth2 提供者服务器
│   └── client/
│       └── index.ts      # OAuth2 客户端应用
├── package.json          # 项目依赖和脚本
├── tsconfig.json         # TypeScript 配置
├── start.sh             # 启动脚本
└── README.md            # 项目文档
```

## 核心功能

### OAuth2 提供者 (端口 3002)

- **授权端点**: `/auth` - 处理授权请求
- **令牌端点**: `/token` - 交换授权码获取访问令牌
- **用户信息端点**: `/me` - 获取用户信息
- **交互端点**: 使用 oidc-provider 默认开发交互 (任何凭据都可以登录)
- **发现端点**: `/.well-known/openid_configuration` - OpenID Connect 发现

### OAuth2 客户端 (端口 3001)

- **主页**: `/` - 客户端应用首页
- **登录**: `/login` - 发起 OAuth2 认证流程
- **回调**: `/callback` - 处理 OAuth2 回调
- **登出**: `/logout` - 用户登出

## 安全特性

### 客户端认证

- **客户端 ID**: `oauth2-client-123` (随机生成)
- **客户端密钥**: 使用 HMAC-SHA256 生成
  ```typescript
  const CLIENT_SECRET = createHmac('sha256', 'secret-key').update(CLIENT_ID).digest('hex')
  ```

### PKCE 支持

- **代码验证器**: 32 字节随机生成，base64url 编码
- **代码挑战**: SHA256 哈希，base64url 编码
- **挑战方法**: S256

### CSRF 保护

- **状态参数**: 32 字节随机生成的十六进制字符串
- **会话验证**: 服务器端验证状态参数

## 配置详情

### OAuth2 提供者配置

```typescript
const configuration = {
  pkce: {
    methods: ['S256'],
    required: () => false, // 演示环境禁用 PKCE 要求
  },
  clients: [
    {
      client_id: 'oauth2-client-123',
      client_secret: 'HMAC生成的密钥',
      redirect_uris: ['http://localhost:3001/callback'],
      grant_types: ['authorization_code', 'refresh_token'],
      response_types: ['code'],
      scope: 'openid profile email',
    },
  ],
  // ... 其他配置
}
```

### 令牌生命周期

- **访问令牌**: 1 小时
- **授权码**: 10 分钟
- **ID 令牌**: 1 小时
- **刷新令牌**: 1 天

## 使用方法

### 启动服务

```bash
# 安装依赖
pnpm install

# 同时启动两个服务
./start.sh

# 或分别启动
pnpm dev      # OAuth2 提供者
pnpm client   # OAuth2 客户端
```

### 测试流程

```bash
# 手动测试
# 1. 访问 http://localhost:3001
# 2. 点击 "Login with OAuth2"
# 3. 使用任何用户名/密码 (开发模式)
# 4. 查看返回的用户信息
```

## OAuth2 认证流程

1. **授权请求**: 客户端重定向用户到提供者的授权端点
2. **用户认证**: 用户在提供者的登录页面输入凭据
3. **授权许可**: 提供者重定向回客户端并携带授权码
4. **令牌交换**: 客户端使用授权码交换访问令牌
5. **资源访问**: 客户端使用访问令牌获取用户信息

## 数据存储

- **会话存储**: 内存存储 (生产环境建议使用 Redis)
- **用户数据**: 内存模拟 (生产环境需要数据库)
- **客户端配置**: 代码配置 (生产环境建议数据库存储)

## 生产环境考虑

1. **数据库集成**: 替换内存存储为持久化数据库
2. **用户管理**: 实现完整的用户认证和管理系统
3. **会话管理**: 使用 Redis 或数据库进行会话存储
4. **HTTPS**: 为所有端点启用 HTTPS
5. **环境变量**: 将密钥移至环境变量
6. **速率限制**: 为认证端点添加速率限制
7. **日志监控**: 实现全面的日志记录和监控

## 项目特点

- ✅ 完整的 OAuth2 认证流程
- ✅ TypeScript 类型安全
- ✅ 现代化的 Hono 框架
- ✅ HMAC 生成的客户端密钥
- ✅ PKCE 安全增强
- ✅ CSRF 保护
- ✅ 无数据库依赖 (演示用途)
- ✅ 详细的文档和测试

## 注意事项

- 本项目仅用于演示和学习目的
- 生产环境需要额外的安全配置
- 开发模式: 任何用户名/密码都可以登录
- 所有密钥和配置都是硬编码的演示值
