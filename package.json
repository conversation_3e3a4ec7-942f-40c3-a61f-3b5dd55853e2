{"name": "oauth2-auth-flow", "version": "1.0.0", "description": "OAuth2 authentication flow with node-oidc-provider and Hono", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "client": "tsx watch src/client/index.ts"}, "keywords": ["oauth2", "oidc", "hono", "typescript"], "author": "", "license": "MIT", "dependencies": {"@hono/node-server": "^1.12.0", "@modelcontextprotocol/sdk": "^1.15.1", "cors": "^2.8.5", "express": "^5.1.0", "hono": "^4.8.5", "oidc-provider": "^8.4.6", "zod": "^4.0.5"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^20.10.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}}