{"name": "oauth2-auth-flow", "version": "1.0.0", "description": "OAuth2 authentication flow with node-oidc-provider and Hono", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "client": "tsx watch src/client/index.ts", "mcp-server": "tsx src/mcp-server.ts", "mcp-inspector": "npx @modelcontextprotocol/inspector node dist/mcp-server.js", "protected-resource": "tsx src/protected-resource-server.ts", "dev:all": "concurrently \"npm run dev\" \"npm run client\" \"npm run protected-resource\""}, "keywords": ["oauth2", "oidc", "hono", "typescript"], "author": "", "license": "MIT", "dependencies": {"@hono/node-server": "^1.12.0", "@modelcontextprotocol/sdk": "^1.15.1", "hono": "^4.8.5", "oidc-provider": "^8.4.6", "open": "^10.2.0", "zod": "^3.25.76"}, "devDependencies": {"@modelcontextprotocol/inspector": "^0.16.1", "@types/node": "^20.10.0", "@types/oidc-provider": "^9.1.1", "concurrently": "^9.2.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}}