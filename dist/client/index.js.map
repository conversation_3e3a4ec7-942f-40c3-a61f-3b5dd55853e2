{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/client/index.ts"], "names": [], "mappings": ";;AAAA,+BAA4B;AAC5B,mDAA0C;AAC1C,mCAA6D;AAG7D,MAAM,GAAG,GAAG,IAAI,WAAI,EAAE,CAAC;AAEvB,8BAA8B;AAC9B,MAAM,SAAS,GAAG,mBAAmB,CAAC;AACtC,MAAM,aAAa,GAAG,IAAA,mBAAU,EAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACzF,MAAM,YAAY,GAAG,gCAAgC,CAAC;AACtD,MAAM,sBAAsB,GAAG,4BAA4B,CAAC;AAC5D,MAAM,cAAc,GAAG,6BAA6B,CAAC;AACrD,MAAM,iBAAiB,GAAG,0BAA0B,CAAC;AAErD,kEAAkE;AAClE,MAAM,QAAQ,GAAwB,EAAE,CAAC;AAEzC,mCAAmC;AACnC,MAAM,UAAU,GAAqC,EAAE,CAAC;AAExD,4CAA4C;AAC5C,SAAS,aAAa;IACpB,OAAO,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC;AAED,4CAA4C;AAC5C,SAAS,YAAY;IACnB,MAAM,YAAY,GAAG,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAC3D,MAAM,aAAa,GAAG,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACpF,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,CAAC;AACzC,CAAC;AAED,YAAY;AACZ,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE;IACjB,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxE,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAEvD,IAAI,OAAO,EAAE,WAAW,EAAE,CAAC;QACzB,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;YACtC,gDAAgD,CAAC,CAAC;YAClD,kDAAkD,CAAC;QAErD,OAAO,CAAC,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;mBAqBC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;;;;;uBAKrC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;;;;yCAIlB,SAAS;cACpC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;;;;;;;;;;aAUxB,CAAC,CAAC,CAAC;;aAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA0CR,CAAC,CAAC;IACL,CAAC;IAED,OAAO,CAAC,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;2CAgB2B,SAAS;8CACN,YAAY;;;;;GAKvD,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,uBAAuB;AACvB,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE;IACtB,MAAM,KAAK,GAAG,aAAa,EAAE,CAAC;IAC9B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,YAAY,EAAE,CAAC;IACvD,MAAM,SAAS,GAAG,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAElD,4DAA4D;IAC5D,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;IAE9C,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,sBAAsB,CAAC,CAAC;IAChD,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;IAClD,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IACjD,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;IACvD,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC;IAC1D,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACzC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;IAC1D,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC;IAE1D,qBAAqB;IACrB,CAAC,CAAC,MAAM,CAAC,YAAY,EAAE,WAAW,SAAS,kCAAkC,CAAC,CAAC;IAE/E,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC;AAEH,yBAAyB;AACzB,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;IAC/B,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACjC,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACnC,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAEnC,IAAI,KAAK,EAAE,CAAC;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,iBAAiB,KAAK,EAAE,EAAE,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;QACpB,OAAO,CAAC,CAAC,IAAI,CAAC,qCAAqC,EAAE,GAAG,CAAC,CAAC;IAC5D,CAAC;IAED,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxE,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAEvD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;QACxC,OAAO,CAAC,CAAC,IAAI,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,CAAC;QACH,+CAA+C;QAC/C,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,cAAc,EAAE;YAChD,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,mCAAmC;gBACnD,eAAe,EAAE,SAAS,MAAM,CAAC,IAAI,CAAC,GAAG,SAAS,IAAI,aAAa,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;aAC5F;YACD,IAAI,EAAE,IAAI,eAAe,CAAC;gBACxB,UAAU,EAAE,oBAAoB;gBAChC,IAAI;gBACJ,YAAY,EAAE,YAAY;gBAC1B,aAAa,EAAE,OAAO,CAAC,YAAY;aACpC,CAAC;SACH,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC;YACtB,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,IAAI,EAAE,CAAC;YAC7C,OAAO,CAAC,CAAC,IAAI,CAAC,0BAA0B,SAAS,EAAE,EAAE,GAAG,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,IAAI,EAAS,CAAC;QAEpD,uBAAuB;QACvB,MAAM,gBAAgB,GAAG,MAAM,KAAK,CAAC,iBAAiB,EAAE;YACtD,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,SAAS,CAAC,YAAY,EAAE;aACpD;SACF,CAAC,CAAC;QAEH,IAAI,QAAQ,GAAQ,EAAE,CAAC;QACvB,IAAI,gBAAgB,CAAC,EAAE,EAAE,CAAC;YACxB,QAAQ,GAAG,MAAM,gBAAgB,CAAC,IAAI,EAAE,CAAC;QAC3C,CAAC;QAED,wCAAwC;QACxC,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,YAAY,CAAC;QAC7C,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,aAAa,CAAC;QAC/C,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC;QACrC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAE5B,0CAA0C;QAC1C,IAAI,SAAS,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC;gBACH,6CAA6C;gBAC7C,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;gBACzB,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,iBAAiB,CAAC,CAAC;gBAC9D,EAAE,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;gBACjD,OAAO,CAAC,GAAG,CAAC,4CAA4C,SAAS,EAAE,CAAC,CAAC;gBACrE,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;gBAChE,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QACxC,OAAO,CAAC,CAAC,IAAI,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,yBAAyB;AACzB,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;IACrC,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxE,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACvD,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAE3D,IAAI,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC;QACxC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,wCAAwC,EAAE,EAAE,GAAG,CAAC,CAAC;IAC1E,CAAC;IAED,IAAI,CAAC;QACH,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACpC,IAAI,MAAM,CAAC;QAEX,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,KAAK;gBACR,MAAM,GAAG,MAAM,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACzD,MAAM;YACR,KAAK,UAAU;gBACb,MAAM,GAAG,MAAM,SAAS,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC9D,MAAM;YACR,KAAK,eAAe;gBAClB,MAAM,GAAG,MAAM,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;gBACnD,MAAM;YACR;gBACE,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,GAAG,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC7C,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,GAAG,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,4BAA4B;AAC5B,GAAG,CAAC,GAAG,CAAC,mBAAmB,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;IACvC,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxE,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACvD,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAE3D,IAAI,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC;QACxC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,wCAAwC,EAAE,EAAE,GAAG,CAAC,CAAC;IAC1E,CAAC;IAED,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,MAAM,SAAS,CAAC,eAAe,EAAE,CAAC;QACvD,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACjD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,GAAG,CAAC,CAAC;IAClE,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,SAAS;AACT,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE;IACxB,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxE,IAAI,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QACrC,iCAAiC;QACjC,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACzB,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;YAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,iBAAiB,CAAC,CAAC;YAC9D,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC7B,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;QAED,wBAAwB;QACxB,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1B,UAAU,CAAC,SAAS,CAAC,CAAC,UAAU,EAAE,CAAC;YACnC,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC;QACD,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC7B,CAAC;IAED,CAAC,CAAC,MAAM,CAAC,YAAY,EAAE,uCAAuC,CAAC,CAAC;IAChE,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC,CAAC,CAAC;AAEH,MAAM,IAAI,GAAG,IAAI,CAAC;AAClB,OAAO,CAAC,GAAG,CAAC,6CAA6C,IAAI,EAAE,CAAC,CAAC;AAEjE,IAAA,mBAAK,EAAC;IACJ,KAAK,EAAE,GAAG,CAAC,KAAK;IAChB,IAAI;CACL,CAAC,CAAC"}