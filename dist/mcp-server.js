"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mcp_js_1 = require("@modelcontextprotocol/sdk/server/mcp.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
const zod_1 = require("zod");
const child_process_1 = require("child_process");
const fs_1 = require("fs");
const path_1 = require("path");
// Authentication state management
class AuthManager {
    tokenFile = (0, path_1.join)(process.cwd(), '.mcp-auth-token');
    accessToken = null;
    isAuthenticated() {
        return this.accessToken !== null || (0, fs_1.existsSync)(this.tokenFile);
    }
    setAccessToken(token) {
        this.accessToken = token;
        (0, fs_1.writeFileSync)(this.tokenFile, token);
    }
    getAccessToken() {
        if (this.accessToken) {
            return this.accessToken;
        }
        if ((0, fs_1.existsSync)(this.tokenFile)) {
            try {
                this.accessToken = (0, fs_1.readFileSync)(this.tokenFile, 'utf8').trim();
                return this.accessToken;
            }
            catch {
                return null;
            }
        }
        return null;
    }
    getUserInfo() {
        const userInfoFile = (0, path_1.join)(process.cwd(), '.mcp-user-info');
        if ((0, fs_1.existsSync)(userInfoFile)) {
            try {
                return JSON.parse((0, fs_1.readFileSync)(userInfoFile, 'utf8'));
            }
            catch {
                return null;
            }
        }
        return null;
    }
    clearToken() {
        this.accessToken = null;
        if ((0, fs_1.existsSync)(this.tokenFile)) {
            require('fs').unlinkSync(this.tokenFile);
        }
    }
    async startOAuth2Services() {
        console.error("🚀 Starting OAuth2 services...");
        const authProcess = (0, child_process_1.spawn)('pnpm', ['run', 'dev:all'], {
            detached: true,
            stdio: 'ignore'
        });
        // Wait for services to start
        await new Promise(resolve => setTimeout(resolve, 3000));
    }
}
const authManager = new AuthManager();
// Create an MCP server with OAuth2 authorization capability
const server = new mcp_js_1.McpServer({
    name: "demo-server",
    version: "1.0.0"
}, {
    capabilities: {
        authorization: {
            oauth2: {
                // According to RFC 8414, this should point to the authorization server metadata
                authorizationUrl: "http://localhost:3002/.well-known/oauth-authorization-server",
                tokenUrl: "http://localhost:3002/token",
                scopes: ["openid", "profile", "email"]
            }
        }
    }
});
// Add OAuth2 authorization handlers according to MCP specification
const AuthRequestSchema = zod_1.z.object({
    method: zod_1.z.literal("auth/request"),
    params: zod_1.z.optional(zod_1.z.object({}))
});
const AuthCompleteSchema = zod_1.z.object({
    method: zod_1.z.literal("auth/complete"),
    params: zod_1.z.object({
        accessToken: zod_1.z.string()
    })
});
server.server.setRequestHandler(AuthRequestSchema, async () => {
    console.error("🔐 OAuth2 authorization requested");
    // Start OAuth2 services if not running
    await authManager.startOAuth2Services();
    return {
        authorizationUrl: "http://localhost:3001/login",
        state: "mcp-oauth-state-" + Date.now()
    };
});
server.server.setRequestHandler(AuthCompleteSchema, async (request) => {
    console.error("✅ OAuth2 authorization completed");
    const { accessToken } = request.params;
    if (!accessToken) {
        throw new Error("No access token provided");
    }
    // Store the access token
    authManager.setAccessToken(accessToken);
    return {
        success: true
    };
});
// Add an addition tool (exactly as in Quick Start, with OAuth2 check)
server.registerTool("add", {
    title: "Addition Tool",
    description: "Add two numbers",
    inputSchema: { a: zod_1.z.number(), b: zod_1.z.number() }
}, async ({ a, b }) => {
    // Check authentication before executing
    if (!authManager.isAuthenticated()) {
        throw new Error("Authentication required. Please complete OAuth2 authorization first.");
    }
    const userInfo = authManager.getUserInfo();
    const userName = userInfo?.name || "Anonymous";
    return {
        content: [{
                type: "text",
                text: `Hello ${userName}! ${a} + ${b} = ${a + b}`
            }]
    };
});
// Add a user info tool
server.registerTool("get-user-info", {
    title: "Get User Info",
    description: "Get authenticated user information",
    inputSchema: {}
}, async () => {
    // Check authentication before executing
    if (!authManager.isAuthenticated()) {
        throw new Error("Authentication required. Please complete OAuth2 authorization first.");
    }
    const userInfo = authManager.getUserInfo();
    const token = authManager.getAccessToken();
    return {
        content: [{
                type: "text",
                text: JSON.stringify({
                    ...userInfo,
                    token: token?.substring(0, 20) + "...",
                    authenticated: true,
                    timestamp: new Date().toISOString()
                }, null, 2)
            }]
    };
});
// Add a dynamic greeting resource (exactly as in Quick Start, with OAuth2 check)
server.registerResource("greeting", new mcp_js_1.ResourceTemplate("greeting://{name}", { list: undefined }), {
    title: "Greeting Resource", // Display name for UI
    description: "Dynamic greeting generator"
}, async (uri, { name }) => {
    // Check authentication before accessing resource
    if (!authManager.isAuthenticated()) {
        throw new Error("Authentication required for resources. Please authenticate first.");
    }
    return {
        contents: [{
                uri: uri.href,
                text: `Hello, ${name}!`
            }]
    };
});
// Start receiving messages on stdin and sending messages on stdout (exactly as in Quick Start)
async function main() {
    const transport = new stdio_js_1.StdioServerTransport();
    await server.connect(transport);
}
main().catch(console.error);
//# sourceMappingURL=mcp-server.js.map