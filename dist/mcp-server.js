"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mcp_js_1 = require("@modelcontextprotocol/sdk/server/mcp.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
const zod_1 = require("zod");
const child_process_1 = require("child_process");
const fs_1 = require("fs");
const path_1 = require("path");
// Authentication state management
class AuthManager {
    tokenFile = (0, path_1.join)(process.cwd(), '.mcp-auth-token');
    isAuthenticated() {
        return (0, fs_1.existsSync)(this.tokenFile);
    }
    async triggerOAuth2Flow() {
        console.error("🔐 Authentication required. Starting OAuth2 flow...");
        // Start OAuth2 services if not running
        const authProcess = (0, child_process_1.spawn)('pnpm', ['run', 'dev:all'], {
            detached: true,
            stdio: 'ignore'
        });
        // Wait a moment for services to start
        await new Promise(resolve => setTimeout(resolve, 3000));
        // Open browser for authentication
        try {
            const open = await Promise.resolve().then(() => __importStar(require('open')));
            await open.default('http://localhost:3001');
            console.error("📱 Browser opened for authentication. Please complete the OAuth2 flow.");
        }
        catch (error) {
            console.error("❌ Failed to open browser. Please manually open: http://localhost:3001");
        }
        console.error("⏳ Waiting for authentication to complete...");
        // Poll for authentication completion
        for (let i = 0; i < 60; i++) { // Wait up to 60 seconds
            await new Promise(resolve => setTimeout(resolve, 1000));
            if (this.isAuthenticated()) {
                console.error("✅ Authentication completed successfully!");
                return true;
            }
        }
        console.error("❌ Authentication timeout. Please try again.");
        return false;
    }
}
const authManager = new AuthManager();
// Create an MCP server (exactly as in Quick Start)
const server = new mcp_js_1.McpServer({
    name: "demo-server",
    version: "1.0.0"
});
// Add an addition tool (exactly as in Quick Start, with OAuth2 check)
server.registerTool("add", {
    title: "Addition Tool",
    description: "Add two numbers",
    inputSchema: { a: zod_1.z.number(), b: zod_1.z.number() }
}, async ({ a, b }) => {
    // Check authentication before executing
    if (!authManager.isAuthenticated()) {
        const authenticated = await authManager.triggerOAuth2Flow();
        if (!authenticated) {
            throw new Error("Authentication required. Please complete OAuth2 flow.");
        }
    }
    return {
        content: [{ type: "text", text: String(a + b) }]
    };
});
// Add a dynamic greeting resource (exactly as in Quick Start, with OAuth2 check)
server.registerResource("greeting", new mcp_js_1.ResourceTemplate("greeting://{name}", { list: undefined }), {
    title: "Greeting Resource", // Display name for UI
    description: "Dynamic greeting generator"
}, async (uri, { name }) => {
    // Check authentication before accessing resource
    if (!authManager.isAuthenticated()) {
        throw new Error("Authentication required for resources. Please authenticate first.");
    }
    return {
        contents: [{
                uri: uri.href,
                text: `Hello, ${name}!`
            }]
    };
});
// Start receiving messages on stdin and sending messages on stdout (exactly as in Quick Start)
async function main() {
    const transport = new stdio_js_1.StdioServerTransport();
    await server.connect(transport);
}
main().catch(console.error);
//# sourceMappingURL=mcp-server.js.map