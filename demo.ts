#!/usr/bin/env tsx

import { McpClientManager } from './src/mcp-client.js';

async function demo() {
  console.log('🚀 Starting MCP Demo...\n');
  
  const client = new McpClientManager();
  
  try {
    // Connect to MCP server
    console.log('📡 Connecting to MCP server...');
    const connected = await client.connect();
    
    if (!connected) {
      console.error('❌ Failed to connect to MCP server');
      return;
    }
    
    console.log('✅ Connected to MCP server!\n');
    
    // Test connection
    console.log('🧪 Testing connection...');
    const testResult = await client.testConnection();
    console.log(`Connection test: ${testResult ? '✅ PASS' : '❌ FAIL'}\n`);
    
    // Get capabilities
    console.log('📋 Getting MCP capabilities...');
    const capabilities = await client.getCapabilities();
    console.log('Available capabilities:');
    console.log(`- Tools: ${capabilities.tools.length}`);
    console.log(`- Resources: ${capabilities.resources.length}`);
    console.log(`- Prompts: ${capabilities.prompts.length}\n`);
    
    // List tools
    console.log('🔧 Available Tools:');
    capabilities.tools.forEach((tool: any) => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });
    console.log();
    
    // Test addition tool
    console.log('➕ Testing Addition Tool...');
    const addResult = await client.callTool('add', { a: 15, b: 27 });
    console.log(`Result: ${addResult.content[0].text}\n`);
    
    // Test multiplication tool
    console.log('✖️ Testing Multiplication Tool...');
    const multiplyResult = await client.callTool('multiply', { a: 8, b: 9 });
    console.log(`Result: ${multiplyResult.content[0].text}\n`);
    
    // Test user info tool
    console.log('👤 Testing User Info Tool...');
    const userInfoResult = await client.callTool('get-user-info');
    console.log(`Result:\n${userInfoResult.content[0].text}\n`);
    
    // List resources
    console.log('📚 Available Resources:');
    capabilities.resources.forEach((resource: any) => {
      console.log(`  - ${resource.name}: ${resource.description}`);
    });
    console.log();
    
    // Test greeting resource
    console.log('👋 Testing Greeting Resource...');
    const greetingResult = await client.readResource('greeting://Alice');
    console.log(`Result: ${greetingResult.contents[0].text}\n`);
    
    // Test user profile resource
    console.log('📄 Testing User Profile Resource...');
    const profileResult = await client.readResource('profile://current-user');
    console.log(`Result:\n${profileResult.contents[0].text}\n`);
    
    // List prompts
    console.log('💭 Available Prompts:');
    capabilities.prompts.forEach((prompt: any) => {
      console.log(`  - ${prompt.name}: ${prompt.description}`);
    });
    console.log();
    
    // Test code review prompt
    console.log('🔍 Testing Code Review Prompt...');
    const codeReviewResult = await client.getPrompt('review-code', {
      code: `function add(a, b) {
  return a + b;
}`
    });
    console.log('Prompt generated:');
    console.log(codeReviewResult.messages[0].content.text);
    
  } catch (error) {
    console.error('❌ Demo failed:', error);
  } finally {
    // Disconnect
    console.log('\n🔌 Disconnecting from MCP server...');
    await client.disconnect();
    console.log('✅ Disconnected successfully');
  }
}

// Run demo if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  demo().catch(console.error);
}

export { demo };
