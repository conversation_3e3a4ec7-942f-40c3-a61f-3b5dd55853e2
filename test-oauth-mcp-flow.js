#!/usr/bin/env node

/**
 * Test script to demonstrate the correct OAuth2 + MCP flow:
 * 1. MCP client tries to connect
 * 2. MCP server detects no authentication, opens browser
 * 3. User completes OAuth2 flow
 * 4. MCP connection succeeds
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Testing OAuth2 + MCP Integration Flow\n');

// Clean up any existing auth token
const tokenFile = path.join(process.cwd(), '.mcp-auth-token');
if (fs.existsSync(tokenFile)) {
  fs.unlinkSync(tokenFile);
  console.log('🧹 Cleared existing auth token');
}

console.log('📋 Expected Flow:');
console.log('1. MCP client attempts to connect');
console.log('2. MCP server detects no authentication');
console.log('3. <PERSON>rows<PERSON> opens for OAuth2 authentication');
console.log('4. User completes OAuth2 flow');
console.log('5. MCP connection succeeds with authenticated tools\n');

console.log('🔧 Starting MCP server...');

// Spawn the MCP server
const mcpServer = spawn('pnpm', ['exec', 'tsx', 'src/simple-mcp-server.ts'], {
  stdio: ['pipe', 'pipe', 'pipe']
});

// Test messages
const messages = [
  // Initialize
  {
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    }
  },
  // Try to call a tool (this should trigger OAuth2 flow)
  {
    jsonrpc: "2.0",
    id: 2,
    method: "tools/call",
    params: {
      name: "add",
      arguments: { a: 10, b: 5 }
    }
  }
];

let messageIndex = 0;
let authFlowTriggered = false;

mcpServer.stdout.on('data', (data) => {
  const lines = data.toString().split('\n').filter(line => line.trim());
  
  lines.forEach(line => {
    if (line.startsWith('{')) {
      try {
        const response = JSON.parse(line);
        
        console.log(`📨 Response ${response.id}:`);
        if (response.result) {
          if (response.result.capabilities) {
            console.log(`   ✅ Server initialized with capabilities: ${Object.keys(response.result.capabilities).join(', ')}`);
            
            // Send the tool call that should trigger OAuth2
            console.log('\n🔐 Attempting to call tool without authentication...');
            mcpServer.stdin.write(JSON.stringify(messages[1]) + '\n');
          } else if (response.result.content) {
            console.log(`   ✅ Tool call successful: ${response.result.content[0].text}`);
            console.log('\n🎉 OAuth2 + MCP integration working correctly!');
            
            mcpServer.kill();
            process.exit(0);
          }
        } else if (response.error) {
          console.log(`   ❌ Error: ${response.error.message}`);
          if (response.error.message.includes('Authentication required')) {
            console.log('\n✅ Authentication check working correctly!');
            console.log('🌐 Browser should have opened for OAuth2 authentication');
            console.log('\n📝 Manual test steps:');
            console.log('1. Complete the OAuth2 flow in the browser');
            console.log('2. Run this test again to see successful tool calls');
            
            mcpServer.kill();
            process.exit(0);
          }
        }
      } catch (e) {
        // Ignore non-JSON lines
      }
    }
  });
});

mcpServer.stderr.on('data', (data) => {
  const message = data.toString();
  console.log(`🔐 Auth: ${message.trim()}`);
  
  if (message.includes('Authentication required')) {
    authFlowTriggered = true;
    console.log('\n✅ OAuth2 flow triggered successfully!');
  }
});

mcpServer.on('error', (error) => {
  console.error('❌ Error starting MCP server:', error);
  process.exit(1);
});

mcpServer.on('close', (code) => {
  if (!authFlowTriggered) {
    console.log('\n❌ OAuth2 flow was not triggered. Check the implementation.');
  }
});

// Start the test
console.log('📤 Initializing MCP connection...');
mcpServer.stdin.write(JSON.stringify(messages[0]) + '\n');

// Timeout after 30 seconds
setTimeout(() => {
  console.log('\n⏰ Test timeout');
  console.log('💡 This is expected if OAuth2 flow was triggered and is waiting for user input');
  mcpServer.kill();
  process.exit(0);
}, 30000);
