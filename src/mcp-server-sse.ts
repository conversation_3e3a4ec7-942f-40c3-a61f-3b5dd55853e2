import { Mcp<PERSON><PERSON><PERSON>, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { spawn } from 'child_process';
import express from 'express';
import cors from 'cors';

// Simple authentication manager for OAuth2 tokens
class AuthManager {
  private accessToken: string | null = null;
  private userInfo: any = null;
  private tokenFile = '.oauth-token';

  setAccessToken(token: string) {
    this.accessToken = token;
    // Save token to file for persistence
    require('fs').writeFileSync(this.tokenFile, token);
  }

  getAccessToken(): string | null {
    if (!this.accessToken) {
      // Try to load from file
      try {
        this.accessToken = require('fs').readFileSync(this.tokenFile, 'utf8');
      } catch (e) {
        // File doesn't exist
      }
    }
    return this.accessToken;
  }

  async getUserInfo(): Promise<any> {
    if (!this.userInfo && this.accessToken) {
      try {
        const response = await fetch("http://localhost:3002/me", {
          headers: {
            "Authorization": `Bearer ${this.accessToken}`
          }
        });
        this.userInfo = await response.json();
      } catch (error) {
        console.error("Failed to fetch user info:", error);
      }
    }
    return this.userInfo;
  }

  isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }

  clearAuth() {
    this.accessToken = null;
    this.userInfo = null;
    try {
      require('fs').unlinkSync(this.tokenFile);
    } catch (e) {
      // File doesn't exist
    }
  }

  async startOAuth2Services(): Promise<void> {
    console.log("🚀 Starting OAuth2 services...");
    const authProcess = spawn('pnpm', ['run', 'dev:all'], {
      detached: true,
      stdio: 'ignore'
    });
    void authProcess;

    // Wait for services to start
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
}

const authManager = new AuthManager();

// Create an MCP server with OAuth2 authorization capability
const server = new McpServer({
  name: "demo-server-sse",
  version: "1.0.0"
}, {
  capabilities: {
    authorization: {
      oauth2: {
        // Point to authorization server metadata
        authorizationUrl: "http://localhost:3002/.well-known/oauth-authorization-server",
        tokenUrl: "http://localhost:3002/token",
        scopes: ["openid", "profile", "email"]
      }
    }
  }
});

// Add OAuth2 authorization handlers
const AuthRequestSchema = z.object({
  method: z.literal("auth/request"),
  params: z.optional(z.object({}))
});

const AuthCompleteSchema = z.object({
  method: z.literal("auth/complete"),
  params: z.object({
    accessToken: z.string()
  })
});

server.server.setRequestHandler(AuthRequestSchema, async () => {
  console.log("🔐 OAuth2 authorization requested");

  // Start OAuth2 services if not running
  await authManager.startOAuth2Services();

  return {
    authorizationUrl: "http://localhost:3001/login",
    state: "mcp-oauth-state-" + Date.now()
  };
});

server.server.setRequestHandler(AuthCompleteSchema, async (request: any) => {
  console.log("✅ OAuth2 authorization completed");

  const { accessToken } = request.params as { accessToken: string };

  if (!accessToken) {
    throw new Error("No access token provided");
  }

  // Store the access token
  authManager.setAccessToken(accessToken);

  return {
    success: true
  };
});

// Add tools with OAuth2 authentication
server.registerTool("add",
  {
    title: "Addition Tool",
    description: "Add two numbers (requires OAuth2 authentication)",
    inputSchema: { a: z.number(), b: z.number() }
  },
  async ({ a, b }) => {
    if (!authManager.isAuthenticated()) {
      throw new Error("Authentication required. Please complete OAuth2 authorization first.");
    }

    const userInfo = await authManager.getUserInfo();
    const userName = userInfo?.name || "Anonymous";

    return {
      content: [{
        type: "text",
        text: `Hello ${userName}! ${a} + ${b} = ${a + b}`
      }]
    };
  }
);

server.registerTool("get-user-info",
  {
    title: "Get User Info",
    description: "Get authenticated user information",
    inputSchema: {}
  },
  async () => {
    if (!authManager.isAuthenticated()) {
      throw new Error("Authentication required. Please complete OAuth2 authorization first.");
    }

    const userInfo = await authManager.getUserInfo();
    const token = authManager.getAccessToken();

    return {
      content: [{
        type: "text",
        text: JSON.stringify({
          ...userInfo,
          token: token?.substring(0, 20) + "...",
          authenticated: true,
          server: "demo-server-sse",
          version: "1.0.0",
          transport: "sse",
          oauth2_supported: true,
          timestamp: new Date().toISOString()
        }, null, 2)
      }]
    };
  }
);

// Add a greeting resource with OAuth2 authentication
server.registerResource(
  "greeting",
  new ResourceTemplate("greeting://{name}", { list: undefined }),
  {
    title: "Greeting Resource",
    description: "Dynamic greeting generator (requires OAuth2 authentication)"
  },
  async (uri, { name }) => {
    if (!authManager.isAuthenticated()) {
      throw new Error("Authentication required for resources. Please authenticate first.");
    }

    const userInfo = await authManager.getUserInfo();
    const userName = userInfo?.name || "Anonymous User";

    return {
      contents: [{
        uri: uri.href,
        text: `Hello, ${name}! Welcome to the MCP demo server. You are authenticated as: ${userName}`
      }]
    };
  }
);

// Start the server using Express + manual HTTP implementation
async function main() {
  const app = express();

  // Add CORS support
  app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  }));

  app.use(express.json());

  // For now, let's create a simple HTTP server that can be used with MCP Inspector
  // This is a simplified approach until we can properly implement SSE transport

  // Health check endpoint
  app.get("/health", (req, res) => {
    res.json({
      status: "ok",
      server: "mcp-server-sse",
      oauth2_supported: true,
      timestamp: new Date().toISOString()
    });
  });

  // OAuth2 metadata endpoint (for MCP Inspector discovery)
  app.get("/.well-known/mcp-oauth-metadata", (req, res) => {
    res.json({
      oauth2: {
        authorizationUrl: "http://localhost:3002/.well-known/oauth-authorization-server",
        tokenUrl: "http://localhost:3002/token",
        scopes: ["openid", "profile", "email"]
      },
      server: {
        name: "demo-server-sse",
        version: "1.0.0",
        transport: "http",
        oauth2_supported: true
      }
    });
  });

  // Simple message endpoint for testing
  app.post("/message", (req, res) => {
    console.log("Received message:", req.body);
    res.json({
      status: "received",
      message: "MCP Server is running with OAuth2 support",
      timestamp: new Date().toISOString()
    });
  });

  // Root endpoint
  app.get("/", (req, res) => {
    res.json({
      message: "MCP Server with OAuth2 Support",
      endpoints: {
        health: "/health",
        metadata: "/.well-known/mcp-oauth-metadata",
        message: "/message"
      },
      oauth2: {
        authorizationUrl: "http://localhost:3002/.well-known/oauth-authorization-server",
        supported: true
      }
    });
  });

  const port = 8082;
  app.listen(port, () => {
    console.log(`🚀 MCP Server with OAuth2 running on http://localhost:${port}`);
    console.log(`🔐 OAuth2 metadata: http://localhost:${port}/.well-known/mcp-oauth-metadata`);
    console.log(`📋 Authorization Server: http://localhost:3002/.well-known/oauth-authorization-server`);
    console.log(`💡 This is a simplified HTTP implementation for OAuth2 testing`);
    console.log(`🔧 For full MCP functionality, use the stdio version with separate OAuth2 services`);
  });
}

main().catch(console.error);
