import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { spawn } from "child_process";
import { readFileSync, writeFileSync, existsSync } from "fs";
import { join } from "path";

// Authentication state management
class AuthManager {
  private tokenFile = join(process.cwd(), '.mcp-auth-token');
  
  isAuthenticated(): boolean {
    return existsSync(this.tokenFile);
  }
  
  getToken(): string | null {
    if (this.isAuthenticated()) {
      try {
        return readFileSync(this.tokenFile, 'utf8').trim();
      } catch {
        return null;
      }
    }
    return null;
  }
  
  async triggerOAuth2Flow(): Promise<boolean> {
    console.error("🔐 Authentication required. Starting OAuth2 flow...");
    
    // Start OAuth2 services if not running
    console.error("🚀 Starting OAuth2 services...");
    const authProcess = spawn('pnpm', ['run', 'dev:all'], {
      detached: true,
      stdio: 'ignore'
    });
    
    // Wait a moment for services to start
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Open browser for authentication
    try {
      const open = await import('open');
      await open.default('http://localhost:3001');
      console.error("📱 Browser opened for authentication. Please complete the OAuth2 flow.");
    } catch (error) {
      console.error("❌ Failed to open browser. Please manually open: http://localhost:3001");
    }
    
    console.error("⏳ Waiting for authentication to complete...");
    
    // Poll for authentication completion
    for (let i = 0; i < 60; i++) { // Wait up to 60 seconds
      await new Promise(resolve => setTimeout(resolve, 1000));
      if (this.isAuthenticated()) {
        console.error("✅ Authentication completed successfully!");
        return true;
      }
    }
    
    console.error("❌ Authentication timeout. Please try again.");
    return false;
  }
}

const authManager = new AuthManager();

// Create an MCP server
const server = new McpServer({
  name: "oauth2-demo-server",
  version: "1.0.0"
});

// Add a simple addition tool with authentication check
server.tool(
  "add",
  "Add two numbers together",
  {
    a: { type: "number", description: "First number" },
    b: { type: "number", description: "Second number" }
  },
  async ({ a, b }) => {
    if (!authManager.isAuthenticated()) {
      const authenticated = await authManager.triggerOAuth2Flow();
      if (!authenticated) {
        throw new Error("Authentication required. Please complete OAuth2 flow at http://localhost:3001");
      }
    }
    
    const token = authManager.getToken();
    return {
      content: [{ 
        type: "text", 
        text: `Authenticated calculation: ${a} + ${b} = ${a + b} (token: ${token?.substring(0, 10)}...)` 
      }]
    };
  }
);

// Add a user info tool
server.tool(
  "get-user-info",
  "Get authenticated user information",
  {},
  async () => {
    if (!authManager.isAuthenticated()) {
      const authenticated = await authManager.triggerOAuth2Flow();
      if (!authenticated) {
        throw new Error("Authentication required. Please complete OAuth2 flow at http://localhost:3001");
      }
    }
    
    const token = authManager.getToken();
    return {
      content: [{
        type: "text",
        text: JSON.stringify({
          authenticated: true,
          token: token?.substring(0, 20) + "...",
          timestamp: new Date().toISOString(),
          message: "Successfully authenticated via OAuth2!"
        }, null, 2)
      }]
    };
  }
);

// Add a greeting resource
server.resource(
  "greeting://hello",
  "A simple greeting",
  "text/plain",
  async () => {
    if (!authManager.isAuthenticated()) {
      throw new Error("Authentication required for resources. Please authenticate first.");
    }
    
    return {
      contents: [{
        uri: "greeting://hello",
        text: "Hello from authenticated MCP server! 🎉"
      }]
    };
  }
);

// Start receiving messages on stdin and sending messages on stdout
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("🚀 OAuth2 + MCP Demo Server connected via stdio");
  console.error("💡 Try calling a tool to trigger OAuth2 authentication flow");
}

// If this file is run directly, start the server
if (process.argv[1].endsWith('simple-mcp-server.ts') || process.argv[1].endsWith('simple-mcp-server.js')) {
  main().catch(console.error);
}
