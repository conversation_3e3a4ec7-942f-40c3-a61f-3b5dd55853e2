import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";

export class McpClientManager {
  private client: Client | null = null;
  private transport: StreamableHTTPClientTransport | null = null;
  private connected = false;

  constructor(private mcpServerUrl: string = "http://localhost:3003/mcp") {}

  async connect(): Promise<boolean> {
    if (this.connected) {
      return true;
    }

    try {
      this.client = new Client({
        name: "oauth2-mcp-client",
        version: "1.0.0"
      });

      this.transport = new StreamableHTTPClientTransport(new URL(this.mcpServerUrl));
      
      await this.client.connect(this.transport);
      this.connected = true;
      
      console.log("Connected to MCP server successfully");
      return true;
    } catch (error) {
      console.error("Failed to connect to MCP server:", error);
      this.cleanup();
      return false;
    }
  }

  async disconnect(): Promise<void> {
    if (this.transport) {
      await this.transport.close();
    }
    this.cleanup();
  }

  private cleanup(): void {
    this.client = null;
    this.transport = null;
    this.connected = false;
  }

  isConnected(): boolean {
    return this.connected;
  }

  async listTools(): Promise<any[]> {
    if (!this.client || !this.connected) {
      throw new Error("MCP client not connected");
    }

    try {
      const result = await this.client.listTools();
      return result.tools || [];
    } catch (error) {
      console.error("Failed to list tools:", error);
      throw error;
    }
  }

  async callTool(name: string, arguments_: Record<string, any> = {}): Promise<any> {
    if (!this.client || !this.connected) {
      throw new Error("MCP client not connected");
    }

    try {
      const result = await this.client.callTool({
        name,
        arguments: arguments_
      });
      return result;
    } catch (error) {
      console.error(`Failed to call tool ${name}:`, error);
      throw error;
    }
  }

  async listResources(): Promise<any[]> {
    if (!this.client || !this.connected) {
      throw new Error("MCP client not connected");
    }

    try {
      const result = await this.client.listResources();
      return result.resources || [];
    } catch (error) {
      console.error("Failed to list resources:", error);
      throw error;
    }
  }

  async readResource(uri: string): Promise<any> {
    if (!this.client || !this.connected) {
      throw new Error("MCP client not connected");
    }

    try {
      const result = await this.client.readResource({ uri });
      return result;
    } catch (error) {
      console.error(`Failed to read resource ${uri}:`, error);
      throw error;
    }
  }

  async listPrompts(): Promise<any[]> {
    if (!this.client || !this.connected) {
      throw new Error("MCP client not connected");
    }

    try {
      const result = await this.client.listPrompts();
      return result.prompts || [];
    } catch (error) {
      console.error("Failed to list prompts:", error);
      throw error;
    }
  }

  async getPrompt(name: string, arguments_: Record<string, any> = {}): Promise<any> {
    if (!this.client || !this.connected) {
      throw new Error("MCP client not connected");
    }

    try {
      const result = await this.client.getPrompt({
        name,
        arguments: arguments_
      });
      return result;
    } catch (error) {
      console.error(`Failed to get prompt ${name}:`, error);
      throw error;
    }
  }

  // Convenience method to get a summary of all available MCP capabilities
  async getCapabilities(): Promise<{
    tools: any[];
    resources: any[];
    prompts: any[];
  }> {
    if (!this.client || !this.connected) {
      throw new Error("MCP client not connected");
    }

    try {
      const [tools, resources, prompts] = await Promise.all([
        this.listTools(),
        this.listResources(),
        this.listPrompts()
      ]);

      return { tools, resources, prompts };
    } catch (error) {
      console.error("Failed to get capabilities:", error);
      throw error;
    }
  }

  // Method to test the connection with a simple tool call
  async testConnection(): Promise<boolean> {
    if (!this.connected) {
      return false;
    }

    try {
      // Try to call the addition tool as a test
      const result = await this.callTool("add", { a: 2, b: 3 });
      return result && result.content && result.content[0]?.text === "5";
    } catch (error) {
      console.error("Connection test failed:", error);
      return false;
    }
  }
}
