import Provider from 'oidc-provider';
import { createHmac } from 'crypto';
import { createServer } from 'http';

// Configuration for OIDC Provider
const configuration = {
  clients: [
    {
      client_id: 'oauth2-client-123',
      client_secret: createHmac('sha256', 'secret-key').update('oauth2-client-123').digest('hex'),
      redirect_uris: ['http://localhost:3001/callback'],
      grant_types: ['authorization_code', 'refresh_token'],
      response_types: ['code'] as const,
      scope: 'openid profile email',
      token_endpoint_auth_method: 'client_secret_basic',
    },
  ],

  cookies: {
    keys: ['some-secret-key-for-cookies'],
  },
  claims: {
    profile: ['name', 'family_name', 'given_name'],
    email: ['email', 'email_verified'],
  },
  features: {
    devInteractions: { enabled: true }, // Enable default dev interactions
    deviceFlow: { enabled: false },
    introspection: { enabled: true },
    revocation: { enabled: true },
  },
  ttl: {
    AccessToken: 1 * 60 * 60, // 1 hour
    AuthorizationCode: 10 * 60, // 10 minutes
    IdToken: 1 * 60 * 60, // 1 hour
    RefreshToken: 1 * 24 * 60 * 60, // 1 day
  },
  findAccount: async (ctx: any, id: string) => {
    // Mock user data
    const users: Record<string, any> = {
      'user123': {
        accountId: 'user123',
        profile: {
          name: 'John Doe',
          email: '<EMAIL>',
          email_verified: true,
        },
      },
    };

    const user = users[id];
    if (!user) return undefined;

    return {
      accountId: id,
      async claims() {
        return user.profile;
      },
    };
  },
};

// Create OIDC Provider instance
const oidc = new Provider('http://localhost:3002', configuration as any);

const port = 3002;
const server = createServer(oidc.callback());

server.listen(port, () => {
  console.log(`Simple OAuth2 Provider running on http://localhost:${port}`);
  console.log(`Authorization endpoint: http://localhost:${port}/auth`);
  console.log(`Token endpoint: http://localhost:${port}/token`);
  console.log(`Interaction endpoint: http://localhost:${port}/interaction/:uid`);
  console.log('');
  console.log('This version uses oidc-provider default dev interactions.');
  console.log('Default credentials: any username/password will work in dev mode.');
});
