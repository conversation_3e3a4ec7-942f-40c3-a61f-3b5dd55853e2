import { createServer } from 'http';

// Simple HTTP server to serve Protected Resource Metadata (RFC 9728)
const port = 8080;

const server = createServer((req, res) => {
  // Handle CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Serve Protected Resource Metadata according to RFC 9728
  if (req.url === '/.well-known/oauth-protected-resource') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    
    const metadata = {
      // The resource identifier (this MCP server)
      resource: "http://localhost:8080",
      
      // Authorization servers that can issue tokens for this resource
      authorization_servers: ["http://localhost:3002"],
      
      // Scopes supported by this resource
      scopes_supported: ["openid", "profile", "email", "mcp:tools", "mcp:resources"],
      
      // How bearer tokens should be presented
      bearer_methods_supported: ["header"],
      
      // Human-readable name
      resource_name: "MCP Demo Server",
      
      // Documentation URL
      resource_documentation: "http://localhost:8080/docs",
      
      // Additional metadata
      resource_policy_uri: "http://localhost:8080/policy",
      resource_tos_uri: "http://localhost:8080/terms"
    };
    
    res.end(JSON.stringify(metadata, null, 2));
    return;
  }

  // Default response
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ error: 'Not Found' }));
});

server.listen(port, () => {
  console.log(`🛡️  Protected Resource Metadata Server running on http://localhost:${port}`);
  console.log(`📋 Protected Resource Metadata: http://localhost:${port}/.well-known/oauth-protected-resource`);
});
