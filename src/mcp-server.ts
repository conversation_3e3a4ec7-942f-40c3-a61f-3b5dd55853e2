import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { spawn } from "child_process";
import { existsSync } from "fs";
import { join } from "path";

// Authentication state management
class AuthManager {
  private tokenFile = join(process.cwd(), '.mcp-auth-token');

  isAuthenticated(): boolean {
    return existsSync(this.tokenFile);
  }

  async triggerOAuth2Flow(): Promise<boolean> {
    console.error("🔐 Authentication required. Starting OAuth2 flow...");

    // Start OAuth2 services if not running
    const authProcess = spawn('pnpm', ['run', 'dev:all'], {
      detached: true,
      stdio: 'ignore'
    });

    // Wait a moment for services to start
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Open browser for authentication
    try {
      const open = await import('open');
      await open.default('http://localhost:3001');
      console.error("📱 Browser opened for authentication. Please complete the OAuth2 flow.");
    } catch (error) {
      console.error("❌ Failed to open browser. Please manually open: http://localhost:3001");
    }

    console.error("⏳ Waiting for authentication to complete...");

    // Poll for authentication completion
    for (let i = 0; i < 60; i++) { // Wait up to 60 seconds
      await new Promise(resolve => setTimeout(resolve, 1000));
      if (this.isAuthenticated()) {
        console.error("✅ Authentication completed successfully!");
        return true;
      }
    }

    console.error("❌ Authentication timeout. Please try again.");
    return false;
  }
}

const authManager = new AuthManager();

// Create an MCP server (exactly as in Quick Start)
const server = new McpServer({
  name: "demo-server",
  version: "1.0.0"
});

// Add an addition tool (exactly as in Quick Start, with OAuth2 check)
server.registerTool("add",
  {
    title: "Addition Tool",
    description: "Add two numbers",
    inputSchema: { a: z.number(), b: z.number() }
  },
  async ({ a, b }) => {
    // Check authentication before executing
    if (!authManager.isAuthenticated()) {
      const authenticated = await authManager.triggerOAuth2Flow();
      if (!authenticated) {
        throw new Error("Authentication required. Please complete OAuth2 flow.");
      }
    }

    return {
      content: [{ type: "text", text: String(a + b) }]
    };
  }
);

// Add a dynamic greeting resource (exactly as in Quick Start, with OAuth2 check)
server.registerResource(
  "greeting",
  new ResourceTemplate("greeting://{name}", { list: undefined }),
  {
    title: "Greeting Resource",      // Display name for UI
    description: "Dynamic greeting generator"
  },
  async (uri, { name }) => {
    // Check authentication before accessing resource
    if (!authManager.isAuthenticated()) {
      throw new Error("Authentication required for resources. Please authenticate first.");
    }

    return {
      contents: [{
        uri: uri.href,
        text: `Hello, ${name}!`
      }]
    };
  }
);

// Start receiving messages on stdin and sending messages on stdout (exactly as in Quick Start)
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
}

main().catch(console.error);
