import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import { z } from "zod";
import { Hono } from "hono";
import { serve } from "@hono/node-server";
import { cors } from "hono/cors";
import { randomUUID } from "crypto";
import { isInitializeRequest } from "@modelcontextprotocol/sdk/types.js";

// Create an MCP server
const server = new McpServer({
  name: "oauth2-demo-server",
  version: "1.0.0"
});

// Add an addition tool
server.registerTool("add",
  {
    title: "Addition Tool",
    description: "Add two numbers",
    inputSchema: z.object({ a: z.number(), b: z.number() })
  },
  async ({ a, b }) => ({
    content: [{ type: "text", text: String(a + b) }]
  })
);

// Add a multiplication tool
server.registerTool("multiply",
  {
    title: "Multiplication Tool",
    description: "Multiply two numbers",
    inputSchema: z.object({ a: z.number(), b: z.number() })
  },
  async ({ a, b }) => ({
    content: [{ type: "text", text: String(a * b) }]
  })
);

// Add a user info tool that uses OAuth2 context
server.registerTool("get-user-info",
  {
    title: "Get User Info",
    description: "Get authenticated user information",
    inputSchema: z.object({})
  },
  async () => {
    // In a real implementation, you would get this from the authenticated session
    return {
      content: [{
        type: "text",
        text: JSON.stringify({
          name: "John Doe",
          email: "<EMAIL>",
          authenticated: true,
          timestamp: new Date().toISOString()
        }, null, 2)
      }]
    };
  }
);

// Add a dynamic greeting resource
server.registerResource(
  "greeting",
  new ResourceTemplate("greeting://{name}", { list: undefined }),
  { 
    title: "Greeting Resource",
    description: "Dynamic greeting generator"
  },
  async (uri, { name }) => ({
    contents: [{
      uri: uri.href,
      text: `Hello, ${name}! Welcome to the OAuth2 + MCP demo.`
    }]
  })
);

// Add a user profile resource
server.registerResource(
  "user-profile",
  "profile://current-user",
  {
    title: "User Profile",
    description: "Current authenticated user profile",
    mimeType: "application/json"
  },
  async (uri) => ({
    contents: [{
      uri: uri.href,
      text: JSON.stringify({
        id: "user123",
        name: "John Doe",
        email: "<EMAIL>",
        roles: ["user", "demo"],
        lastLogin: new Date().toISOString()
      }, null, 2)
    }]
  })
);

// Add a code review prompt
server.registerPrompt(
  "review-code",
  {
    title: "Code Review",
    description: "Review code for best practices and potential issues",
    argsSchema: z.object({ code: z.string() })
  },
  ({ code }) => ({
    messages: [{
      role: "user",
      content: {
        type: "text",
        text: `Please review this code for best practices, potential issues, and suggestions for improvement:\n\n${code}`
      }
    }]
  })
);

// Function to start MCP server with stdio transport (simpler approach)
export async function startMcpStdioServerDemo() {
  console.log("Starting MCP Server with stdio transport...");
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.log("MCP Server connected via stdio");
}

// Simple HTTP server for testing (without session management for now)
export async function startMcpHttpServer(port: number = 3003) {
  const app = new Hono();

  // Enable CORS
  app.use('*', cors({
    origin: '*',
    allowHeaders: ['Content-Type'],
    exposeHeaders: ['Content-Type'],
  }));

  // Simple health check endpoint
  app.get('/health', (c) => {
    return c.json({ status: 'ok', message: 'MCP Server is running' });
  });

  // For now, let's create a simple API that mimics MCP functionality
  app.post('/mcp/tools/list', async (c) => {
    return c.json({
      tools: [
        { name: 'add', description: 'Add two numbers' },
        { name: 'multiply', description: 'Multiply two numbers' },
        { name: 'get-user-info', description: 'Get authenticated user information' }
      ]
    });
  });

  app.post('/mcp/tools/call', async (c) => {
    const { name, arguments: args } = await c.req.json();

    switch (name) {
      case 'add':
        return c.json({
          content: [{ type: 'text', text: String(args.a + args.b) }]
        });
      case 'multiply':
        return c.json({
          content: [{ type: 'text', text: String(args.a * args.b) }]
        });
      case 'get-user-info':
        return c.json({
          content: [{
            type: 'text',
            text: JSON.stringify({
              name: "John Doe",
              email: "<EMAIL>",
              authenticated: true,
              timestamp: new Date().toISOString()
            }, null, 2)
          }]
        });
      default:
        return c.json({ error: 'Unknown tool' }, 400);
    }
  });

  app.post('/mcp/resources/list', async (c) => {
    return c.json({
      resources: [
        { name: 'greeting', description: 'Dynamic greeting generator' },
        { name: 'user-profile', description: 'Current authenticated user profile' }
      ]
    });
  });

  app.post('/mcp/resources/read', async (c) => {
    const { uri } = await c.req.json();

    if (uri.startsWith('greeting://')) {
      const name = uri.replace('greeting://', '');
      return c.json({
        contents: [{
          uri,
          text: `Hello, ${name}! Welcome to the OAuth2 + MCP demo.`
        }]
      });
    } else if (uri === 'profile://current-user') {
      return c.json({
        contents: [{
          uri,
          text: JSON.stringify({
            id: "user123",
            name: "John Doe",
            email: "<EMAIL>",
            roles: ["user", "demo"],
            lastLogin: new Date().toISOString()
          }, null, 2)
        }]
      });
    } else {
      return c.json({ error: 'Resource not found' }, 404);
    }
  });

  app.post('/mcp/prompts/list', async (c) => {
    return c.json({
      prompts: [
        { name: 'review-code', description: 'Review code for best practices and potential issues' }
      ]
    });
  });

  app.post('/mcp/prompts/get', async (c) => {
    const { name, arguments: args } = await c.req.json();

    if (name === 'review-code') {
      return c.json({
        messages: [{
          role: 'user',
          content: {
            type: 'text',
            text: `Please review this code for best practices, potential issues, and suggestions for improvement:\n\n${args.code}`
          }
        }]
      });
    } else {
      return c.json({ error: 'Prompt not found' }, 404);
    }
  });

  console.log(`MCP Server starting on http://localhost:${port}`);
  console.log(`MCP endpoints:`);
  console.log(`  - Health: http://localhost:${port}/health`);
  console.log(`  - Tools: http://localhost:${port}/mcp/tools/*`);
  console.log(`  - Resources: http://localhost:${port}/mcp/resources/*`);
  console.log(`  - Prompts: http://localhost:${port}/mcp/prompts/*`);

  serve({
    fetch: app.fetch,
    port,
  });
}

// Function to start MCP server with stdio transport (for command line usage)
export async function startMcpStdioServer() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.log("MCP Server connected via stdio");
}

// If this file is run directly, start the HTTP server
if (import.meta.url === `file://${process.argv[1]}`) {
  startMcpHttpServer().catch(console.error);
}
