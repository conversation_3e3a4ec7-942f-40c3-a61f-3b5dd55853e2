import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { spawn } from "child_process";
import { existsSync, writeFileSync, readFileSync } from "fs";
import { join } from "path";

// Authentication state management
class AuthManager {
  private tokenFile = join(process.cwd(), '.mcp-auth-token');
  private accessToken: string | null = null;

  isAuthenticated(): boolean {
    return this.accessToken !== null || existsSync(this.tokenFile);
  }

  setAccessToken(token: string): void {
    this.accessToken = token;
    writeFileSync(this.tokenFile, token);
  }

  getAccessToken(): string | null {
    if (this.accessToken) {
      return this.accessToken;
    }

    if (existsSync(this.tokenFile)) {
      try {
        this.accessToken = readFileSync(this.tokenFile, 'utf8').trim();
        return this.accessToken;
      } catch {
        return null;
      }
    }

    return null;
  }

  getUserInfo(): any {
    const userInfoFile = join(process.cwd(), '.mcp-user-info');
    if (existsSync(userInfoFile)) {
      try {
        return JSON.parse(readFileSync(userInfoFile, 'utf8'));
      } catch {
        return null;
      }
    }
    return null;
  }

  clearToken(): void {
    this.accessToken = null;
    if (existsSync(this.tokenFile)) {
      require('fs').unlinkSync(this.tokenFile);
    }
  }

  async startOAuth2Services(): Promise<void> {
    console.error("🚀 Starting OAuth2 services...");
    const authProcess = spawn('pnpm', ['run', 'dev:all'], {
      detached: true,
      stdio: 'ignore'
    });

    // Wait for services to start
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
}

const authManager = new AuthManager();

// Create a simple MCP server without OAuth2 for testing
const server = new McpServer({
  name: "demo-server",
  version: "1.0.0"
}, {
  capabilities: {
    // Remove OAuth2 for now to test basic functionality
    // authorization: {
    //   oauth2: {
    //     authorizationUrl: "http://localhost:8080/.well-known/oauth-protected-resource",
    //     tokenUrl: "http://localhost:3002/token",
    //     scopes: ["openid", "profile", "email"]
    //   }
    // }
  }
});

// Add OAuth2 authorization handlers according to MCP specification
const AuthRequestSchema = z.object({
  method: z.literal("auth/request"),
  params: z.optional(z.object({}))
});

const AuthCompleteSchema = z.object({
  method: z.literal("auth/complete"),
  params: z.object({
    accessToken: z.string()
  })
});

server.server.setRequestHandler(AuthRequestSchema, async () => {
  console.error("🔐 OAuth2 authorization requested");

  // Start OAuth2 services if not running
  await authManager.startOAuth2Services();

  return {
    authorizationUrl: "http://localhost:3001/login",
    state: "mcp-oauth-state-" + Date.now()
  };
});

server.server.setRequestHandler(AuthCompleteSchema, async (request: any) => {
  console.error("✅ OAuth2 authorization completed");

  const { accessToken } = request.params as { accessToken: string };

  if (!accessToken) {
    throw new Error("No access token provided");
  }

  // Store the access token
  authManager.setAccessToken(accessToken);

  return {
    success: true
  };
});

// Add an addition tool (exactly as in Quick Start, with OAuth2 check)
server.registerTool("add",
  {
    title: "Addition Tool",
    description: "Add two numbers",
    inputSchema: { a: z.number(), b: z.number() }
  },
  async ({ a, b }) => {
    // Check authentication before executing
    if (!authManager.isAuthenticated()) {
      throw new Error("Authentication required. Please complete OAuth2 authorization first.");
    }

    const userInfo = authManager.getUserInfo();
    const userName = userInfo?.name || "Anonymous";

    return {
      content: [{
        type: "text",
        text: `Hello ${userName}! ${a} + ${b} = ${a + b}`
      }]
    };
  }
);

// Add a user info tool
server.registerTool("get-user-info",
  {
    title: "Get User Info",
    description: "Get authenticated user information",
    inputSchema: {}
  },
  async () => {
    // Check authentication before executing
    if (!authManager.isAuthenticated()) {
      throw new Error("Authentication required. Please complete OAuth2 authorization first.");
    }

    const userInfo = authManager.getUserInfo();
    const token = authManager.getAccessToken();

    return {
      content: [{
        type: "text",
        text: JSON.stringify({
          ...userInfo,
          token: token?.substring(0, 20) + "...",
          authenticated: true,
          timestamp: new Date().toISOString()
        }, null, 2)
      }]
    };
  }
);

// Add a dynamic greeting resource (exactly as in Quick Start, with OAuth2 check)
server.registerResource(
  "greeting",
  new ResourceTemplate("greeting://{name}", { list: undefined }),
  {
    title: "Greeting Resource",      // Display name for UI
    description: "Dynamic greeting generator"
  },
  async (uri, { name }) => {
    // Check authentication before accessing resource
    if (!authManager.isAuthenticated()) {
      throw new Error("Authentication required for resources. Please authenticate first.");
    }

    return {
      contents: [{
        uri: uri.href,
        text: `Hello, ${name}!`
      }]
    };
  }
);

// Start receiving messages on stdin and sending messages on stdout (exactly as in Quick Start)
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
}

main().catch(console.error);
