import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { spawn } from "child_process";
import { existsSync, writeFileSync, readFileSync } from "fs";
import { join } from "path";

// Authentication state management
class AuthManager {
  private tokenFile = join(process.cwd(), '.mcp-auth-token');
  private accessToken: string | null = null;

  isAuthenticated(): boolean {
    return this.accessToken !== null || existsSync(this.tokenFile);
  }

  setAccessToken(token: string): void {
    this.accessToken = token;
    writeFileSync(this.tokenFile, token);
  }

  getAccessToken(): string | null {
    if (this.accessToken) {
      return this.accessToken;
    }

    if (existsSync(this.tokenFile)) {
      try {
        this.accessToken = readFileSync(this.tokenFile, 'utf8').trim();
        return this.accessToken;
      } catch {
        return null;
      }
    }

    return null;
  }

  getUserInfo(): any {
    const userInfoFile = join(process.cwd(), '.mcp-user-info');
    if (existsSync(userInfoFile)) {
      try {
        return JSON.parse(readFileSync(userInfoFile, 'utf8'));
      } catch {
        return null;
      }
    }
    return null;
  }

  clearToken(): void {
    this.accessToken = null;
    if (existsSync(this.tokenFile)) {
      require('fs').unlinkSync(this.tokenFile);
    }
  }

  async startOAuth2Services(): Promise<void> {
    console.error("🚀 Starting OAuth2 services...");
    const authProcess = spawn('pnpm', ['run', 'dev:all'], {
      detached: true,
      stdio: 'ignore'
    });

    // Wait for services to start
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
}

const authManager = new AuthManager();

// Create a simple MCP server (stdio transport doesn't support OAuth2)
const server = new McpServer({
  name: "demo-server",
  version: "1.0.0"
}, {
  capabilities: {
    // OAuth2 is not supported with stdio transport according to MCP spec
    // "Implementations using an STDIO transport SHOULD NOT follow this specification"
  }
});

// OAuth2 authorization handlers removed for stdio transport
// According to MCP spec: "Implementations using an STDIO transport SHOULD NOT follow this specification"

// Add a simple addition tool (no OAuth2 required for stdio transport)
server.registerTool("add",
  {
    title: "Addition Tool",
    description: "Add two numbers",
    inputSchema: { a: z.number(), b: z.number() }
  },
  async ({ a, b }) => {
    return {
      content: [{
        type: "text",
        text: `${a} + ${b} = ${a + b}`
      }]
    };
  }
);

// Add a simple info tool (no OAuth2 required)
server.registerTool("get-info",
  {
    title: "Get Server Info",
    description: "Get server information",
    inputSchema: {}
  },
  async () => {
    return {
      content: [{
        type: "text",
        text: JSON.stringify({
          server: "demo-server",
          version: "1.0.0",
          transport: "stdio",
          oauth2_supported: false,
          timestamp: new Date().toISOString()
        }, null, 2)
      }]
    };
  }
);

// Add a simple greeting resource (no OAuth2 required)
server.registerResource(
  "greeting",
  new ResourceTemplate("greeting://{name}", { list: undefined }),
  {
    title: "Greeting Resource",
    description: "Dynamic greeting generator"
  },
  async (uri, { name }) => {
    return {
      contents: [{
        uri: uri.href,
        text: `Hello, ${name}! Welcome to the MCP demo server.`
      }]
    };
  }
);

// Start the server using stdio transport (OAuth2 not supported with stdio)
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.log("🚀 MCP Server running on stdio (OAuth2 not supported with stdio transport)");
  console.log("💡 For OAuth2 support, use HTTP or SSE transport instead");
}

main().catch(console.error);
