import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { spawn } from "child_process";
import { readFileSync, writeFileSync, existsSync } from "fs";
import { join } from "path";

// Authentication state management
class AuthManager {
  private tokenFile = join(process.cwd(), '.mcp-auth-token');

  isAuthenticated(): boolean {
    return existsSync(this.tokenFile);
  }

  getToken(): string | null {
    if (this.isAuthenticated()) {
      try {
        return readFileSync(this.tokenFile, 'utf8').trim();
      } catch {
        return null;
      }
    }
    return null;
  }

  setToken(token: string): void {
    writeFileSync(this.tokenFile, token);
  }

  clearToken(): void {
    if (existsSync(this.tokenFile)) {
      require('fs').unlinkSync(this.tokenFile);
    }
  }

  async triggerOAuth2Flow(): Promise<boolean> {
    console.error("🔐 Authentication required. Starting OAuth2 flow...");

    // Start OAuth2 services if not running
    const authProcess = spawn('pnpm', ['run', 'dev:all'], {
      detached: true,
      stdio: 'ignore'
    });

    // Wait a moment for services to start
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Open browser for authentication
    const open = await import('open');
    await open.default('http://localhost:3001');

    console.error("📱 Browser opened for authentication. Please complete the OAuth2 flow.");
    console.error("⏳ Waiting for authentication to complete...");

    // Poll for authentication completion
    for (let i = 0; i < 60; i++) { // Wait up to 60 seconds
      await new Promise(resolve => setTimeout(resolve, 1000));
      if (this.isAuthenticated()) {
        console.error("✅ Authentication completed successfully!");
        return true;
      }
    }

    console.error("❌ Authentication timeout. Please try again.");
    return false;
  }
}

const authManager = new AuthManager();

// Create an MCP server
const server = new McpServer({
  name: "oauth2-demo-server",
  version: "1.0.0"
});

// Add an addition tool with authentication check
server.registerTool("add",
  {
    title: "Addition Tool",
    description: "Add two numbers",
    inputSchema: z.object({
      a: z.number().describe("First number"),
      b: z.number().describe("Second number")
    })
  },
  async ({ a, b }) => {
    if (!authManager.isAuthenticated()) {
      const authenticated = await authManager.triggerOAuth2Flow();
      if (!authenticated) {
        throw new Error("Authentication required. Please complete OAuth2 flow.");
      }
    }

    return {
      content: [{ type: "text", text: String(a + b) }]
    };
  }
);

// Add a multiplication tool
server.registerTool("multiply",
  {
    title: "Multiplication Tool",
    description: "Multiply two numbers",
    inputSchema: { a: z.number(), b: z.number() }
  },
  async ({ a, b }) => ({
    content: [{ type: "text", text: String(a * b) }]
  })
);

// Add a user info tool that uses OAuth2 context
server.registerTool("get-user-info",
  {
    title: "Get User Info",
    description: "Get authenticated user information",
    inputSchema: {}
  },
  async () => {
    // In a real implementation, you would get this from the authenticated session
    return {
      content: [{
        type: "text",
        text: JSON.stringify({
          name: "John Doe",
          email: "<EMAIL>",
          authenticated: true,
          timestamp: new Date().toISOString()
        }, null, 2)
      }]
    };
  }
);

// Add a dynamic greeting resource
server.registerResource(
  "greeting",
  new ResourceTemplate("greeting://{name}", { list: undefined }),
  {
    title: "Greeting Resource",
    description: "Dynamic greeting generator"
  },
  async (uri, { name }) => ({
    contents: [{
      uri: uri.href,
      text: `Hello, ${name}! Welcome to the OAuth2 + MCP demo.`
    }]
  })
);

// Add a user profile resource
server.registerResource(
  "user-profile",
  "profile://current-user",
  {
    title: "User Profile",
    description: "Current authenticated user profile",
    mimeType: "application/json"
  },
  async (uri) => ({
    contents: [{
      uri: uri.href,
      text: JSON.stringify({
        id: "user123",
        name: "John Doe",
        email: "<EMAIL>",
        roles: ["user", "demo"],
        lastLogin: new Date().toISOString()
      }, null, 2)
    }]
  })
);

// Add a code review prompt
server.registerPrompt(
  "review-code",
  {
    title: "Code Review",
    description: "Review code for best practices and potential issues",
    argsSchema: { code: z.string() }
  },
  ({ code }) => ({
    messages: [{
      role: "user",
      content: {
        type: "text",
        text: `Please review this code for best practices, potential issues, and suggestions for improvement:\n\n${code}`
      }
    }]
  })
);

// Start receiving messages on stdin and sending messages on stdout
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.log("OAuth2 + MCP Demo Server connected via stdio");
}

// If this file is run directly, start the server
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
