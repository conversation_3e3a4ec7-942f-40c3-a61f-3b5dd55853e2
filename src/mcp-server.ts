import { Mcp<PERSON>erver, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { spawn } from "child_process";
import { existsSync, writeFileSync, readFileSync } from "fs";
import { join } from "path";

// Authentication state management
class AuthManager {
  private tokenFile = join(process.cwd(), '.mcp-auth-token');
  private accessToken: string | null = null;

  isAuthenticated(): boolean {
    return this.accessToken !== null || existsSync(this.tokenFile);
  }

  setAccessToken(token: string): void {
    this.accessToken = token;
    writeFileSync(this.tokenFile, token);
  }

  getAccessToken(): string | null {
    if (this.accessToken) {
      return this.accessToken;
    }

    if (existsSync(this.tokenFile)) {
      try {
        this.accessToken = readFileSync(this.tokenFile, 'utf8').trim();
        return this.accessToken;
      } catch {
        return null;
      }
    }

    return null;
  }

  getUserInfo(): any {
    const userInfoFile = join(process.cwd(), '.mcp-user-info');
    if (existsSync(userInfoFile)) {
      try {
        return JSON.parse(readFileSync(userInfoFile, 'utf8'));
      } catch {
        return null;
      }
    }
    return null;
  }

  clearToken(): void {
    this.accessToken = null;
    if (existsSync(this.tokenFile)) {
      require('fs').unlinkSync(this.tokenFile);
    }
  }

  async startOAuth2Services(): Promise<void> {
    // No console output in stdio transport
    const authProcess = spawn('pnpm', ['run', 'dev:all'], {
      detached: true,
      stdio: 'ignore'
    });
    // Suppress unused variable warning
    void authProcess;

    // Wait for services to start
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
}

const authManager = new AuthManager();

// Create an MCP server with OAuth2 authorization capability
const server = new McpServer({
  name: "demo-server",
  version: "1.0.0"
}, {
  capabilities: {
    authorization: {
      oauth2: {
        // Point to our MCP OAuth2 metadata server
        authorizationUrl: "http://localhost:8081/.well-known/mcp-oauth-metadata",
        tokenUrl: "http://localhost:3002/token",
        scopes: ["openid", "profile", "email"]
      }
    }
  }
});

// Add OAuth2 authorization handlers for SSE transport
const AuthRequestSchema = z.object({
  method: z.literal("auth/request"),
  params: z.optional(z.object({}))
});

const AuthCompleteSchema = z.object({
  method: z.literal("auth/complete"),
  params: z.object({
    accessToken: z.string()
  })
});

server.server.setRequestHandler(AuthRequestSchema, async () => {
  // No console output in stdio transport

  // Start OAuth2 services if not running
  await authManager.startOAuth2Services();

  return {
    authorizationUrl: "http://localhost:3001/login",
    state: "mcp-oauth-state-" + Date.now()
  };
});

server.server.setRequestHandler(AuthCompleteSchema, async (request: any) => {
  // No console output in stdio transport

  const { accessToken } = request.params as { accessToken: string };

  if (!accessToken) {
    throw new Error("No access token provided");
  }

  // Store the access token
  authManager.setAccessToken(accessToken);

  return {
    success: true
  };
});

// Add an addition tool with OAuth2 authentication
server.registerTool("add",
  {
    title: "Addition Tool",
    description: "Add two numbers (requires OAuth2 authentication)",
    inputSchema: { a: z.number(), b: z.number() }
  },
  async ({ a, b }) => {
    // Check authentication before executing
    if (!authManager.isAuthenticated()) {
      throw new Error("Authentication required. Please complete OAuth2 authorization first.");
    }

    const userInfo = authManager.getUserInfo();
    const userName = userInfo?.name || "Anonymous";

    return {
      content: [{
        type: "text",
        text: `Hello ${userName}! ${a} + ${b} = ${a + b}`
      }]
    };
  }
);

// Add a user info tool with OAuth2 authentication
server.registerTool("get-user-info",
  {
    title: "Get User Info",
    description: "Get authenticated user information",
    inputSchema: {}
  },
  async () => {
    // Check authentication before executing
    if (!authManager.isAuthenticated()) {
      throw new Error("Authentication required. Please complete OAuth2 authorization first.");
    }

    const userInfo = authManager.getUserInfo();
    const token = authManager.getAccessToken();

    return {
      content: [{
        type: "text",
        text: JSON.stringify({
          ...userInfo,
          token: token?.substring(0, 20) + "...",
          authenticated: true,
          server: "demo-server",
          version: "1.0.0",
          transport: "sse",
          oauth2_supported: true,
          timestamp: new Date().toISOString()
        }, null, 2)
      }]
    };
  }
);

// Add a greeting resource with OAuth2 authentication
server.registerResource(
  "greeting",
  new ResourceTemplate("greeting://{name}", { list: undefined }),
  {
    title: "Greeting Resource",
    description: "Dynamic greeting generator (requires OAuth2 authentication)"
  },
  async (uri, { name }) => {
    // Check authentication before accessing resource
    if (!authManager.isAuthenticated()) {
      throw new Error("Authentication required for resources. Please authenticate first.");
    }

    const userInfo = authManager.getUserInfo();
    const userName = userInfo?.name || "Anonymous User";

    return {
      contents: [{
        uri: uri.href,
        text: `Hello, ${name}! Welcome to the MCP demo server. You are authenticated as: ${userName}`
      }]
    };
  }
);

// Start the server using stdio transport
async function main() {
  // IMPORTANT: No console output in stdio transport - it interferes with JSON-RPC
  const { StdioServerTransport } = await import("@modelcontextprotocol/sdk/server/stdio.js");
  const transport = new StdioServerTransport();
  await server.connect(transport);

  // No logging in stdio transport - it breaks the JSON-RPC protocol
}

main().catch(console.error);
