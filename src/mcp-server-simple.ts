import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

// Create a simple MCP server without OAuth2 (stdio transport doesn't support OAuth2)
const server = new McpServer({
  name: "demo-server-simple",
  version: "1.0.0"
}, {
  capabilities: {
    // No OAuth2 support with stdio transport
  }
});

// Add simple tools without authentication
server.registerTool("add",
  {
    title: "Addition Tool",
    description: "Add two numbers",
    inputSchema: { a: z.number(), b: z.number() }
  },
  async ({ a, b }) => {
    return {
      content: [{
        type: "text",
        text: `${a} + ${b} = ${a + b}`
      }]
    };
  }
);

server.registerTool("multiply",
  {
    title: "Multiplication Tool", 
    description: "Multiply two numbers",
    inputSchema: { a: z.number(), b: z.number() }
  },
  async ({ a, b }) => {
    return {
      content: [{
        type: "text",
        text: `${a} × ${b} = ${a * b}`
      }]
    };
  }
);

server.registerTool("get-server-info",
  {
    title: "Get Server Info",
    description: "Get server information",
    inputSchema: {}
  },
  async () => {
    return {
      content: [{
        type: "text",
        text: JSON.stringify({
          server: "demo-server-simple",
          version: "1.0.0",
          transport: "stdio",
          oauth2_supported: false,
          features: ["tools", "resources"],
          timestamp: new Date().toISOString()
        }, null, 2)
      }]
    };
  }
);

// Add a simple greeting resource
server.registerResource(
  "greeting",
  new ResourceTemplate("greeting://{name}", { list: undefined }),
  {
    title: "Greeting Resource",
    description: "Dynamic greeting generator"
  },
  async (uri, { name }) => {
    return {
      contents: [{
        uri: uri.href,
        text: `Hello, ${name}! Welcome to the simple MCP demo server.`
      }]
    };
  }
);

// Add a time resource
server.registerResource(
  "time",
  new ResourceTemplate("time://current", { list: undefined }),
  {
    title: "Current Time",
    description: "Get current server time"
  },
  async (uri) => {
    return {
      contents: [{
        uri: uri.href,
        text: `Current server time: ${new Date().toISOString()}`
      }]
    };
  }
);

// Start the server using stdio transport
async function main() {
  // IMPORTANT: No console output in stdio transport - it interferes with JSON-RPC
  const transport = new StdioServerTransport();
  await server.connect(transport);
  
  // No logging in stdio transport - it breaks the JSON-RPC protocol
}

main().catch(() => {
  // Silent error handling for stdio transport
  process.exit(1);
});
