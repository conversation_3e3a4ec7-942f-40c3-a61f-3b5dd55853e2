# OAuth2 + MCP Integration Demo

A complete OAuth2 authentication flow integrated with Model Context Protocol (MCP) using node-oidc-provider, Hono, and the MCP TypeScript SDK.

## Features

- **OAuth2 Provider** using oidc-provider
- **OAuth2 Client** using Hono
- **MCP Server** with tools, resources, and prompts
- **MCP Client** that connects after OAuth2 authentication
- PKCE (Proof Key for Code Exchange) support
- Session management
- User authentication and token handling
- Interactive MCP tool testing

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   OAuth2        │    │   OAuth2        │    │   MCP Server    │
│   Provider      │◄──►│   Client        │◄──►│   (Tools &      │
│   (Port 3002)   │    │   (Port 3001)   │    │   Resources)    │
└─────────────────┘    └─────────────────┘    │   (Port 3003)   │
                                              └─────────────────┘
```

## Setup

1. Install dependencies:

```bash
pnpm install
```

2. Start all services simultaneously:

```bash
pnpm run dev:all
```

Or start them individually:

```bash
# Terminal 1: OAuth2 Provider
pnpm run dev

# Terminal 2: OAuth2 Client
pnpm run client

# Terminal 3: MCP Server
pnpm run mcp-server
```

## Usage

1. Open http://localhost:3001 in your browser
2. Click "Login with OAuth2"
3. You'll be redirected to the OAuth2 provider at http://localhost:3002
4. Use any username/password (dev mode accepts any credentials)
5. After authentication, you'll be redirected back to the client
6. The MCP client will automatically connect to the MCP server
7. Test MCP tools using the interactive buttons

## MCP Features

### Available Tools

- **Addition Tool**: Add two numbers
- **Multiplication Tool**: Multiply two numbers
- **Get User Info**: Get authenticated user information

### Available Resources

- **Greeting Resource**: Dynamic greeting generator (`greeting://{name}`)
- **User Profile**: Current authenticated user profile (`profile://current-user`)

### Available Prompts

- **Code Review**: Review code for best practices and potential issues

## Endpoints

### OAuth2 Provider (Port 3002)

- Authorization: `http://localhost:3002/auth`
- Token: `http://localhost:3002/token`
- User Info: `http://localhost:3002/me`
- Interaction: `http://localhost:3002/interaction/:uid`

### OAuth2 Client (Port 3001)

- Home: `http://localhost:3001/`
- Login: `http://localhost:3001/login`
- Callback: `http://localhost:3001/callback`
- Logout: `http://localhost:3001/logout`
- MCP Test Tool: `http://localhost:3001/mcp/test-tool`
- MCP Capabilities: `http://localhost:3001/mcp/capabilities`

### MCP Server (Port 3003)

- MCP Endpoint: `http://localhost:3003/mcp`

## How It Works

1. **OAuth2 Authentication**: User authenticates via OAuth2 flow
2. **Session Management**: Authenticated session is stored with tokens
3. **MCP Connection**: After successful authentication, MCP client automatically connects to MCP server
4. **Tool Interaction**: User can interact with MCP tools through the web interface
5. **Resource Access**: MCP resources and prompts are available for use

## Dependencies

- **@modelcontextprotocol/sdk**: MCP TypeScript SDK
- **oidc-provider**: OpenID Connect provider implementation
- **hono**: Fast web framework for TypeScript
- **zod**: TypeScript-first schema validation
- **tsx**: TypeScript execution environment
