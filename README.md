# OAuth2 + MCP Integration Demo

A complete OAuth2 authentication flow integrated with Model Context Protocol (MCP) using node-oidc-provider, Hono, and the MCP TypeScript SDK.

## Features

- **OAuth2 Provider** using oidc-provider
- **OAuth2 Client** using Hono
- **MCP Server** with tools, resources, and prompts
- **MCP Client** that connects after OAuth2 authentication
- PKCE (Proof Key for Code Exchange) support
- Session management
- User authentication and token handling
- Interactive MCP tool testing

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   OAuth2        │    │   OAuth2        │    │   MCP Server    │
│   Provider      │◄──►│   Client        │◄──►│   (stdio)       │
│   (Port 3002)   │    │   (Port 3001)   │    │   Tools &       │
└─────────────────┘    └─────────────────┘    │   Resources     │
                                              └─────────────────┘
```

The MCP server uses stdio transport for communication, which is spawned as a child process by the OAuth2 client when a user successfully authenticates.

## Setup

1. Install dependencies:

```bash
pnpm install
```

2. Build the project (required for MCP Inspector):

```bash
pnpm run build
```

3. Start all services:

```bash
pnpm run dev:all
```

Or start them individually:

```bash
# Terminal 1: OAuth2 Provider
pnpm run dev

# Terminal 2: OAuth2 Client
pnpm run client
```

Note: The MCP server is automatically spawned by the OAuth2 client when needed, so no separate terminal is required.

## Usage

### Method 1: Direct MCP Client Connection (Recommended)

This demonstrates the intended flow where MCP clients (like Cursor, Claude CLI) connect directly:

1. Try to connect to the MCP server:

   ```bash
   node test-oauth-mcp-flow.js
   ```

2. The MCP server will detect no authentication and automatically:

   - Start the OAuth2 services
   - Open your browser for authentication
   - Wait for you to complete the OAuth2 flow

3. Complete the OAuth2 authentication in the browser (any username/password works)

4. The MCP connection will then succeed and tools will be available

### Method 2: Using MCP Inspector (Recommended for Testing)

1. Build the project: `pnpm run build`
2. Start MCP Inspector: `pnpm run mcp-inspector`
3. The browser will open automatically with the MCP Inspector interface
4. Try calling the "add" tool - this will trigger the OAuth2 authentication flow
5. Complete the OAuth2 authentication in the opened browser window
6. Return to MCP Inspector to see the tool working with authentication

### Method 3: Manual Web Interface

1. Start the services: `pnpm run dev:all`
2. Open http://localhost:3001 in your browser
3. Click "Login with OAuth2"
4. Complete authentication
5. Test MCP tools using the web interface

## MCP Features

### Available Tools

- **Addition Tool**: Add two numbers
- **Multiplication Tool**: Multiply two numbers
- **Get User Info**: Get authenticated user information

### Available Resources

- **Greeting Resource**: Dynamic greeting generator (`greeting://{name}`)
- **User Profile**: Current authenticated user profile (`profile://current-user`)

### Available Prompts

- **Code Review**: Review code for best practices and potential issues

## Endpoints

### OAuth2 Provider (Port 3002)

- Authorization: `http://localhost:3002/auth`
- Token: `http://localhost:3002/token`
- User Info: `http://localhost:3002/me`
- Interaction: `http://localhost:3002/interaction/:uid`

### OAuth2 Client (Port 3001)

- Home: `http://localhost:3001/`
- Login: `http://localhost:3001/login`
- Callback: `http://localhost:3001/callback`
- Logout: `http://localhost:3001/logout`
- MCP Test Tool: `http://localhost:3001/mcp/test-tool`
- MCP Capabilities: `http://localhost:3001/mcp/capabilities`

### MCP Server (stdio)

- Communication: stdio transport (spawned as child process)
- Test manually: `pnpm run mcp-server`

## How It Works

### Current Implementation (Simplified OAuth2)

1. **MCP Connection**: Client connects to MCP server successfully
2. **Tool Call**: Client attempts to call a tool (e.g., "add")
3. **Authentication Check**: MCP server checks for valid authentication token
4. **OAuth2 Flow**: If not authenticated, server returns error with instructions
5. **Manual Authentication**: User manually completes OAuth2 flow at http://localhost:3001
6. **Token Storage**: Authentication token is stored for MCP server access
7. **Tool Success**: Subsequent tool calls work with authentication

### MCP Specification Compliant Flow (Future)

According to the [MCP Authorization Specification](https://modelcontextprotocol.io/specification/draft/basic/authorization), the proper flow should be:

1. **Server Declares OAuth2**: Server announces OAuth2 support in `initialize` response
2. **Client Requests Auth**: Client sends `auth/request` message
3. **Server Returns URL**: Server responds with OAuth2 authorization URL
4. **Client Opens Browser**: Client opens browser for user authentication
5. **Client Sends Token**: Client sends access token via `auth/complete` message
6. **Authenticated Access**: All subsequent requests are authenticated

**Note**: The current MCP TypeScript SDK may not fully support the authorization specification yet, so we've implemented a simplified version that demonstrates the OAuth2 integration concept.

## Dependencies

- **@modelcontextprotocol/sdk**: MCP TypeScript SDK
- **oidc-provider**: OpenID Connect provider implementation
- **hono**: Fast web framework for TypeScript
- **zod**: TypeScript-first schema validation
- **tsx**: TypeScript execution environment
